.task-queue-container {
  width: 100%;
  height: 100%;
  background: #edf3fd;
}
.task-queue-content {
  width: 100%;
  max-width: 1440px;
  height: 100%;
  margin: 0 auto;
  padding: 32px;
}
.task-queue-list {
  width: 100%;
  padding-right: 16px;
  max-height: calc(100vh - 150px);
  overflow-y: auto;
}
.task-card {
  position: relative;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #e6e6e6;
  margin-bottom: 20px;
  padding: 24px 48px;
  transition:
    border 0.2s,
    background 0.2s;
  .task-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    .task-new {
      position: absolute;
      top: 0;
      left: 0;
      width: 40px; // 角标区域宽度
      height: 40px; // 角标区域高度
      pointer-events: none;
      z-index: 2;

      // 红色三角形
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        border-top: 40px solid #f5222d; // 红色高度
        border-right: 40px solid transparent; // 透明宽度
        border-radius: 12px 0 0 0; // 保持和卡片圆角一致
        z-index: 1;
      }

      // “新”字
      &::after {
        content: '新';
        position: absolute;
        top: 4px;
        left: 6px;
        color: #fff;
        font-size: 12px;
        z-index: 2;
      }
    }
    .task-title {
      font-size: 16px;
      color: #262628;
    }
    .task-card-actions {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 8px;
      .task-card-refresh-button {
        border-radius: 4px;
        border: 1px solid #ff8e51;
        background: #ff8e51;
        color: #fff;
        &:hover {
          background: #ff8e51;
          color: #fff;
          border: 1px solid #ff8e51;
        }
      }
    }
  }
  .task-card-body {
    width: 70%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    .task-info-left,
    .task-info-center,
    .task-info-right {
      display: flex;
      flex-direction: column;
      gap: 10px;
      .task-info-left-item,
      .task-info-center-item,
      .task-info-right-item {
        color: #262628;
      }
      .task-info-left-title,
      .task-info-center-title,
      .task-info-right-title {
        color: #6b7280;
      }
    }
  }
}
.task-card.running {
  border: 1px solid #5b8ff9;
  box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
}
.ant-tag.running {
  color: #2868e7;
  background: #e2ecff;
  border: none;
  border-radius: 18px;
  padding: 4px 16px;
}
.ant-tag.completed {
  color: #06801c;
  background: #e2ffef;
  border: none;
  border-radius: 18px;
  padding: 4px 16px;
}
.ant-tag.failed {
  color: #800606;
  background: #ffe2e2;
  border: none;
  border-radius: 18px;
  padding: 4px 16px;
}
.task-queue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .header-left {
    display: flex;
    align-items: center;
    .header-icon {
      width: 32px;
      height: 32px;
      margin-right: 6px;
      cursor: pointer;
    }
    .header-title {
      font-size: 20px;
      font-weight: bold;
      color: #000;
    }
  }
  .header-right {
    display: flex;
    align-items: center;
    gap: 10px;
    .task-queue-refresh-button {
      border-radius: 4px;
      border: none;
      background: rgba(114, 159, 248, 0.2);
      color: #2868e7;
      &:hover {
        border: #000;
        background: rgba(114, 159, 248, 0.2);
        color: #2868e7;
      }
    }
  }
}
