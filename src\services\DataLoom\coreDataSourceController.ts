// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 POST /admin/coreDatasource/add */
export async function addDatasource(formData: FormData, options?: { [key: string]: any }) {
  return request<API.BaseResponseLong>('/admin/coreDatasource/add', {
    method: 'POST',
    // 不设置Content-Type，浏览器会自动设置为multipart/form-data
    data: formData,
    ...(options || {}),
  });
}

/** 校验数据源 POST /admin/coreDatasource/check */
export async function checkDatasource(body: API.DatasourceDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/coreDatasource/check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /admin/coreDatasource/get */
export async function getDataSource(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDataSourceParams,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseDatasourceDTO>('/admin/coreDatasource/get', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /admin/coreDatasource/getTableFields */
export async function getTableFieldsByDatasourceIdAndTableName(body: API.GetTableFieldsDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseListCoreDatasetTableField>('/admin/coreDatasource/getTableFields', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取数据源所有表信息 GET /admin/coreDatasource/getTables */
export async function getTablesByDatasourceId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTablesByDatasourceIdParams,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseListCoreDatasetTable>('/admin/coreDatasource/getTables', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /admin/coreDatasource/handleApiResponse */
export async function handleApiResponse(body: API.ApiDefinition, options?: { [key: string]: any }) {
  return request<API.BaseResponseApiDefinition>('/admin/coreDatasource/handleApiResponse', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 展示用户数据源列表 GET /admin/coreDatasource/list */
export async function listUserDataSource(params?: { [key: string]: any }) {
  return request<any>('/admin/coreDatasource/list', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 深度问数选择数据源 GET /admin/Ai/get/dataSources */
export async function getAiDataSources(options?: { [key: string]: any }) {
  return request<API.BaseResponseListCoreDatasource>('/admin/Ai/get/dataSources', {
    method: 'GET',
    ...(options || {}),
  });
}
/** 删除 & 批量删除数据源 POST /admin/coreDatasource/delete */
export async function deleteDatasource(body: any, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/coreDatasource/delete', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}
