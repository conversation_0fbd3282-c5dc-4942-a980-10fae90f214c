// Icon背景色推荐及容错工具
export const ICON_BG_COLORS = [
  '#4173C8',
  '#6DB36A',
  '#F29C50',
  '#7DD3E7',
  '#2B53B3',
  '#7B5DC7',
  '#F6E08D',
  '#B06FC7',
  '#5B7DD6',
  '#7DC7A0',
  '#F27B7A',
  '#D6C86B',
  '#7DB6E7',
  '#E38DC7',
  '#A3C7A0',
  '#F27B7A',
  '#D6C86B',
  '#7DB6E7',
  '#FF8A65',
  '#4DB6AC',
  '#FFD54F',
  '#9575CD',
  '#81C784',
  '#BA68C8',
  '#7986CB',
  '#AED581',
  '#F06292',
  '#4FC3F7',
  '#FFD740',
  '#A1887F',
  '#90A4AE',
  '#FFB74D',
  '#64B5F6',
  '#E57373',
  '#81D4FA',
  '#DCE775',
  '#AEEA00',
  '#00B8D4',
  '#C5E1A5',
  '#FF80AB',
  '#B2DFDB',
  '#FFAB91',
  '#B0BEC5',
  '#D1C4E9',
  '#C8E6C9',
  '#FFF59D',
  '#F8BBD0',
  '#B3E5FC',
  '#DCEDC8',
  '#FFE082',
  '#FFCCBC',
];

// 随机生成一个16进制颜色
export function getRandomColor(): string {
  return (
    '#' +
    Math.floor(Math.random() * 0xffffff)
      .toString(16)
      .padStart(6, '0')
  );
}

// 获取指定数量的背景色，不够则自动补充随机色
export function getIconBgColors(count: number): string[] {
  const colors = [...ICON_BG_COLORS];
  while (colors.length < count) {
    colors.push(getRandomColor());
  }
  return colors.slice(0, count);
}

// 图片映射配置
export const IMAGE_MAP: Record<string, string> = {
  data_analyzer: '/assets/role/image_1753263163035_3jdp4s.svg',
  data_cleaner: '/assets/role/image_1753259495045_qvhggj.svg',
  machine_learning_assistant: '/assets/role/image_1753259494597_gjwoa1.svg',
  bio_info_analyzer: '/assets/role/image_1753259494156_i9gbzk.svg',
  echarts_generator: '/assets/role/image_1753259493691_wyjxg8.svg',
  device_doc_retriever: '/assets/role/image_1753259493017_p484zi.svg',
  default: '/assets/image_1752222659877_mlbnx2.svg',
};

// 根据type获取对应的图片路径
export const getImageByType = (type: string) => {
  return IMAGE_MAP[type] || IMAGE_MAP['default'];
};

// 检查type是否有对应的图片（不是default）
export const hasCustomImage = (type: string) => {
  return IMAGE_MAP.hasOwnProperty(type) && type !== 'default';
};
