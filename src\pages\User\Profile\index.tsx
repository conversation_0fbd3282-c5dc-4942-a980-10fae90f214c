import React, { useState, useEffect } from 'react';
import { Button, Input, Modal, Form, message, Card, Drawer, Row, Col } from 'antd';
import { history, useLocation } from '@umijs/max';
import './index.less';
import { updateUserPassword, updateUserTel } from '@/services/DataLoom/userController';
import { useModel } from '@umijs/max';

// 路由名称映射
const routeNameMap: { [key: string]: string } = {
  '/welcome': '首页',
  '/mysql_detail/:id': '数据源',
  '/dashboard': '仪表盘',
  '/ai_chat': '深度问数',
  '/system': '系统管理',
  '/mobile': '移动端页面',
};

const Profile: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser || {};
  const [showPwdModal, setShowPwdModal] = useState(false);
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [phoneValue, setPhoneValue] = useState(currentUser.tel || '');
  const [newPassword, setNewPassword] = useState('');
  const [form] = Form.useForm();
  const location = useLocation();
  const [previousRouteName, setPreviousRouteName] = useState<string>('系统管理');

  // 密码强度计算
  const getPasswordStrength = (pwd: string) => {
    if (!pwd) return 0;
    let score = 0;
    if (pwd.length >= 6) score += 1;
    if (/[A-Z]/.test(pwd) && /[a-z]/.test(pwd)) score += 1;
    if (/[0-9]/.test(pwd) && /[^A-Za-z0-9]/.test(pwd)) score += 1;
    return score;
  };
  const strength = getPasswordStrength(newPassword);
  const strengthMap = ['弱', '中', '强'];

  // 获取上一个页面的路由信息
  useEffect(() => {
    // 从URL参数获取来源页面
    const urlParams = new URLSearchParams(location.search);
    const from = urlParams.get('from');

    if (from) {
      setPreviousRouteName(getRouteName(from));
    } else {
      // 从localStorage获取上一个页面路径
      const storedPath = localStorage.getItem('previousPath');
      if (storedPath && storedPath !== location.pathname) {
        setPreviousRouteName(getRouteName(storedPath));
      }
    }

    // 存储当前路径
    localStorage.setItem('previousPath', location.pathname);
  }, [location.pathname, location.search]);

  // 根据路径获取路由名称
  const getRouteName = (path: string): string => {
    // 处理带参数的路径，只取基础路径
    const basePath = path.split('/').slice(0, 3).join('/');
    return routeNameMap[basePath] || '未知页面';
  };

  const handlePwdOk = async () => {
    try {
      const values = await form.validateFields();
      const res = await updateUserPassword({
        id: currentUser.id ? String(currentUser.id) : '',
        oldPassword: values.oldPwd,
        newPassword: values.newPwd,
      });
      if (res && res.code === 0 && res.data === true) {
        message.success('密码修改成功');
        setShowPwdModal(false);
        setNewPassword('');
        form.resetFields();
      } else {
        message.error(res?.message || '密码修改失败');
      }
    } catch (err) {
      // 校验失败自动提示，无需处理
    }
  };

  const handleBack = () => {
    // 从URL参数获取来源页面
    const urlParams = new URLSearchParams(location.search);
    const from = urlParams.get('from');

    if (from) {
      history.push(from);
    } else {
      // 从localStorage获取上一个页面路径
      const storedPath = localStorage.getItem('previousPath');
      if (storedPath && storedPath !== location.pathname) {
        history.push(storedPath);
      } else {
        // 默认返回系统管理
        history.push('/system');
      }
    }
  };

  const handlePhoneEdit = () => {
    setIsEditingPhone(true);
  };

  const handlePhoneSave = async () => {
    // 手机号正则校验（中国大陆手机号）
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(phoneValue)) {
      message.error('请输入有效的手机号');
      return;
    }
    try {
      const res = await updateUserTel({
        id: currentUser.id ? String(currentUser.id) : '',
        tel: phoneValue,
      });
      if (res && res.code === 0 && res.data === true) {
        message.success('手机号修改成功');
        setIsEditingPhone(false);
        // 这里可以同步更新 currentUser.tel，如果有全局状态的话
      } else {
        message.error(res?.message || '手机号修改失败');
      }
    } catch (err) {
      message.error('手机号修改失败');
    }
  };

  const handlePhoneCancel = () => {
    setPhoneValue(currentUser.tel || '');
    setIsEditingPhone(false);
  };

  return (
    <div className="profile-page-bg">
      <Card className="full-screen-card">
        <div className="profile-title">
          <span className="profile-title-parent" onClick={handleBack} style={{ cursor: 'pointer' }}>
            {previousRouteName}
          </span>
          <span className="profile-title-text">/账号信息</span>
          <div className="profile-title-back" onClick={handleBack}>
            <img src="/assets/image_1752030619419_fa174z.svg" alt="返回" />
          </div>
        </div>
        <div className="profile-container">
          <div className="profile-row">
            <span className="profile-label">账号名称</span>
            <span className="profile-value">{currentUser.userName || '-'}</span>
          </div>
          <div className="profile-row">
            <span className="profile-label">密码</span>
            <span className="profile-value">*********</span>
            <Button
              type="default"
              onClick={() => setShowPwdModal(true)}
              style={{
                background: 'rgba(114, 159, 248, 0.20)',
                color: '#2868E7',
                border: 'none',
                marginLeft: 24,
              }}
            >
              修改密码
            </Button>
          </div>
          <div className="profile-row">
            <span className="profile-label">角色</span>
            <span className="profile-value">{currentUser.userRole || '-'}</span>
          </div>
          <div className="profile-row">
            <span className="profile-label">使用科室</span>
            <span className="profile-value">{currentUser.department || '-'}</span>
          </div>
          <div className="profile-row">
            <span className="profile-label">使用人</span>
            <span className="profile-value">{currentUser.userName || '-'}</span>
          </div>
          <div className="profile-row">
            <span className="profile-label">职位</span>
            <span className="profile-value">{currentUser.position || '-'}</span>
          </div>
          <div className="profile-row">
            <span className="profile-label">联系方式</span>
            {isEditingPhone ? (
              <Input value={phoneValue} onChange={(e) => setPhoneValue(e.target.value)} style={{ width: 200, marginRight: 8 }} />
            ) : (
              <span className="profile-value">{phoneValue || '-'}</span>
            )}
            {isEditingPhone ? (
              <>
                <Button
                  type="default"
                  onClick={handlePhoneSave}
                  style={{
                    background: 'rgba(114, 159, 248, 0.20)',
                    color: '#2868E7',
                    border: 'none',
                    marginLeft: 8,
                  }}
                >
                  保存
                </Button>
                <Button
                  type="default"
                  onClick={handlePhoneCancel}
                  style={{
                    background: 'rgba(255, 255, 255, 0.20)',
                    color: '#666',
                    border: '1px solid #d9d9d9',
                    marginLeft: 8,
                  }}
                >
                  取消
                </Button>
              </>
            ) : (
              <Button
                type="default"
                onClick={handlePhoneEdit}
                style={{
                  background: 'rgba(114, 159, 248, 0.20)',
                  color: '#2868E7',
                  border: 'none',
                  marginLeft: 24,
                }}
              >
                修改手机号
              </Button>
            )}
          </div>
        </div>
      </Card>

      <Modal
        title="修改密码"
        className="common-modal"
        open={showPwdModal}
        width={500}
        onCancel={() => {
          setShowPwdModal(false);
          setNewPassword('');
          form.resetFields();
        }}
        onOk={handlePwdOk}
      >
        <Form form={form} layout="vertical">
          <Form.Item label="旧密码" name="oldPwd" rules={[{ required: true, message: '请输入旧密码' }]}>
            <Input.Password placeholder="请输入旧密码" />
          </Form.Item>
          <Form.Item
            label="新密码"
            name="newPwd"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度不能少于6个字符' },
              { max: 20, message: '密码长度不能超过20个字符' },
            ]}
          >
            <Input.Password placeholder="设置新密码" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} />
          </Form.Item>
          <Row align="middle" style={{ marginTop: -10, marginBottom: 16 }}>
            <Col flex="auto">
              <div style={{ display: 'flex', gap: '4px', width: '100%' }}>
                <div
                  style={{
                    flex: 1,
                    height: '8px',
                    backgroundColor: strength >= 1 ? '#ff4d4f' : '#f5f5f5',
                    borderRadius: '4px',
                  }}
                />
                <div
                  style={{
                    flex: 1,
                    height: '8px',
                    backgroundColor: strength >= 2 ? '#faad14' : '#f5f5f5',
                    borderRadius: '4px',
                  }}
                />
                <div
                  style={{
                    flex: 1,
                    height: '8px',
                    backgroundColor: strength >= 3 ? '#52c41a' : '#f5f5f5',
                    borderRadius: '4px',
                  }}
                />
              </div>
            </Col>
            <Col
              style={{
                textAlign: 'right',
                marginLeft: 6,
                color: strength === 1 ? '#ff4d4f' : strength === 2 ? '#faad14' : strength === 3 ? '#52c41a' : '#999',
              }}
            >
              {newPassword ? strengthMap[strength - 1] || '弱' : ''}
            </Col>
          </Row>
          <Form.Item
            label="再次输入密码"
            name="confirmPwd"
            dependencies={['newPwd']}
            rules={[
              { required: true, message: '请再次输入新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPwd') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Profile;
