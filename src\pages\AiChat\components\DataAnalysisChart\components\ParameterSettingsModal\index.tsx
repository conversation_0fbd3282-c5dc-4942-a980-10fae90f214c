import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Space } from 'antd';
import * as echarts from 'echarts/core';
import type { EChartsCoreOption } from 'echarts/core';

import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import ChartSettings, { ThemeConfigValues } from '@/components/ChartSettings';
import { CHART_STYLE } from '@/config/charts';
import { applyChartSettings } from '@/utils/chartSettings';
import './index.less';

// 注册必要的组件
echarts.use([BarChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent, CanvasRenderer]);

interface ParameterSettingsModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (settings: ThemeConfigValues, chart: EChartsCoreOption) => void;
  chartStyle?: string;
  chartOption?: EChartsCoreOption;
  chartType?: string;
}

const ParameterSettingsModal: React.FC<ParameterSettingsModalProps> = ({
  visible,
  onCancel,
  onOk,
  chartStyle,
  chartOption,
  chartType = 'bar',
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chart, setChart] = useState<echarts.ECharts | null>(null);
  const [isFirstRender, setIsFirstRender] = useState(true);

  const [settings, setSettings] = useState<ThemeConfigValues>(() => {
    return CHART_STYLE;
  });
  const [settingsChartOption, setSettingsChartOption] = useState<EChartsCoreOption>(chartOption as EChartsCoreOption);

  // 监听 chartStyle 变化
  useEffect(() => {
    if (chartStyle) {
      try {
        const parsedStyle = JSON.parse(chartStyle);
        setSettings(parsedStyle);
      } catch (error) {
        console.error('解析 chartStyle 失败:', error);
      }
    }
  }, [chartStyle]);

  // 更新图表配置
  const updateChartOption = (newSettings: ThemeConfigValues) => {
    if (!chart || !chartOption) return;

    // 使用封装的工具函数应用设置
    const newOption = applyChartSettings(chartOption, newSettings, chartType);

    chart.setOption(newOption, true);
    setSettingsChartOption(newOption);
    chart.resize();
  };

  // 初始化图表
  useEffect(() => {
    let chartInstance: echarts.ECharts | null = null;
    let resizeHandler: (() => void) | null = null;

    const initChart = () => {
      if (!chartRef.current) {
        return;
      }

      // 清理旧的图表实例
      if (chart) {
        chart.dispose();
      }

      // 创建新的图表实例
      chartInstance = echarts.init(chartRef.current);
      setChart(chartInstance);

      if (chartOption) {
        chartInstance.setOption(chartOption, true);
      }

      // 添加resize事件监听
      resizeHandler = () => chartInstance?.resize();
      window.addEventListener('resize', resizeHandler);
    };

    if (visible) {
      setTimeout(() => {
        initChart();
      }, 0);
    }

    // 清理函数
    return () => {
      if (resizeHandler) {
        window.removeEventListener('resize', resizeHandler);
      }
      if (chartInstance) {
        chartInstance.dispose();
      }
    };
  }, [visible, chartOption]);

  // 当设置改变时更新图表
  useEffect(() => {
    if (chart && visible) {
      if (isFirstRender) {
        setIsFirstRender(false);
        return;
      }
      updateChartOption(settings);
    }
  }, [settings, chart, visible]);

  // 处理确认
  const handleConfirm = () => {
    onOk(settings, settingsChartOption);
  };

  // 处理取消
  const handleCancel = () => {
    if (chart) {
      chart.dispose();
      setChart(null);
    }
    setIsFirstRender(true);
    onCancel();
  };

  return (
    <Modal
      title="参数设置"
      open={visible}
      onCancel={handleCancel}
      onOk={handleConfirm}
      width={1000}
      className="chart-settings-modal common-modal"
    >
      <div className="modal-content" style={{ minHeight: '500px' }}>
        <div className="chart-preview" style={{ height: '400px', width: '100%', marginBottom: '20px' }}>
          <div ref={chartRef} style={{ width: '100%', height: '100%', minHeight: '400px' }}></div>
        </div>

        <ChartSettings
          onChange={(newSettings) => {
            setSettings(newSettings);
          }}
          defaultValues={settings}
          chartType={chartType as any}
          layout="horizontal"
        />
      </div>
    </Modal>
  );
};

export default ParameterSettingsModal;
