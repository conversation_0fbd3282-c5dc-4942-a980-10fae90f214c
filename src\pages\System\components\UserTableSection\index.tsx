import React, { useState, useEffect } from 'react';
import { Button, Input, Select, Space, Table, Pagination, Popconfirm, message, Modal, Switch } from 'antd';
import AddUserModal from './compontents/AddUserModal';
import BatchAddUserModal from './compontents/BatchAddUserModal';
import EditUserModal from './compontents/EditUserModal';
import ResetPasswordModal from './compontents/ResetPasswordModal';
import './index.less';
import {
  listUserByPage,
  deleteUser,
  updateUserStatus,
  addUser,
  updateUserByAdmin,
  resetUserPassword,
} from '@/services/DataLoom/userController';

const { Option } = Select;

// 用户数据接口 - 基于API.User类型
interface UserData {
  id?: number;
  userAccount?: string;
  userPassword?: string;
  userName?: string;
  email?: string;
  invitationCode?: string;
  userAvatar?: string;
  userRole?: string;
  totalRewardPoints?: number;
  createTime?: string;
  updateTime?: string;
  isDelete?: number;
  vipexpirationTime?: string;
  svipexpirationTime?: string;
  // 扩展字段
  department?: string;
  position?: string;
  tel?: string;
  lastLogin?: string;
  status?: number; // 0-禁用，1-正常
}

// 查询参数接口
interface QueryParams {
  current: number;
  pageSize: number;
  userName?: string;
  userRole?: string;
  status?: string;
}

const UserTableSection: React.FC = () => {
  // 弹窗状态
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [batchAddModalVisible, setBatchAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editUser, setEditUser] = useState<UserData | null>(null);
  const [resetPwdModalVisible, setResetPwdModalVisible] = useState(false);
  const [resetPwdAccount, setResetPwdAccount] = useState<string | undefined>(undefined);
  const [resetPwdId, setResetPwdId] = useState<number | undefined>(undefined);

  // 表格状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);
  const [userList, setUserList] = useState<UserData[]>([]);
  const [total, setTotal] = useState(0);

  // 查询参数
  const [queryParams, setQueryParams] = useState<QueryParams>({
    current: 1,
    pageSize: 10,
  });

  // 筛选条件
  const [filters, setFilters] = useState({
    status: '全部',
    userName: '',
  });

  // 获取用户列表
  const fetchUserList = async (params: QueryParams) => {
    try {
      setLoading(true);
      const response = await listUserByPage({
        current: params.current,
        pageSize: params.pageSize,
        userName: params.userName || undefined,
        userRole: params.userRole || undefined,
      });

      if (response.code === 0 && response.data) {
        setUserList(response.data.records || []);
        setTotal(response.data.total || 0);
      } else {
        message.error(response.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 智能刷新函数 - 处理删除后的分页逻辑
  const refreshUserList = (isDeleteOperation: boolean = false, deleteCount: number = 1) => {
    if (isDeleteOperation) {
      // 删除操作：先尝试加载当前页，如果当前页没有数据了，再跳转到上一页
      const currentPage = queryParams.current;
      const currentPageDataCount = userList.length;

      // 如果删除的数量大于等于当前页的数据量，且不是第一页，则跳转到上一页
      if (deleteCount >= currentPageDataCount && currentPage > 1) {
        const newParams = { ...queryParams, current: currentPage - 1 };
        setQueryParams(newParams);
        fetchUserList(newParams);
      } else {
        // 否则重新加载当前页数据
        fetchUserList(queryParams);
      }
    } else {
      // 非删除操作：直接刷新当前页
      fetchUserList(queryParams);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchUserList(queryParams);
  }, []);

  // 删除用户
  const handleDelete = async (record: UserData) => {
    if (!record.id) {
      message.error('用户ID不存在');
      return;
    }
    try {
      const response = await deleteUser([record.id.toString()]);
      if (response.code === 0 && response.data) {
        message.success('删除成功');
        refreshUserList(true, 1); // 删除1条数据
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error('删除失败，请稍后重试');
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    Modal.confirm({
      title: '确认批量删除选中账号吗？',
      content: '此操作不可恢复，是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 将选中的ID转换为数字数组
          const ids = selectedRowKeys.map((id) => id.toString());
          const deleteCount = selectedRowKeys.length;

          const response = await deleteUser(ids);
          if (response.code === 0 && response.data) {
            message.success('批量删除成功');
            setSelectedRowKeys([]);
            refreshUserList(true, deleteCount); // 传递删除的数量
          } else {
            message.error(response.message || '批量删除失败');
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败，请稍后重试');
        }
      },
    });
  };

  // 更新用户状态
  const handleStatusChange = async (record: UserData, checked: boolean) => {
    console.log('record', record);
    if (!record.id) {
      message.error('用户ID不存在');
      return;
    }
    try {
      const response = await updateUserStatus({
        id: record.id.toString(),
        status: checked ? '1' : '0',
      });

      if (response.code === 0 && response.data) {
        message.success(checked ? '启用成功' : '禁用成功');
        // 更新本地数据
        setUserList((prev) => prev.map((user) => (user.id === record.id ? { ...user, status: checked ? 1 : 0 } : user)));
      } else {
        message.error(response.message || '状态更新失败');
      }
    } catch (error) {
      console.error('更新用户状态失败:', error);
      message.error('状态更新失败，请稍后重试');
    }
  };

  // 查询
  const handleSearch = () => {
    const newParams = {
      ...queryParams,
      current: 1, // 重置到第一页
      userName: filters.userName || undefined,
      userRole: filters.status !== '全部' ? filters.status : undefined,
    };
    setQueryParams(newParams);
    fetchUserList(newParams);
  };

  // 重置筛选
  const handleReset = () => {
    const newFilters = { status: '全部', userName: '' };
    setFilters(newFilters);
    const newParams = {
      current: 1,
      pageSize: queryParams.pageSize,
    };
    setQueryParams(newParams);
    fetchUserList(newParams);
  };

  // 分页变化
  const handlePageChange = (page: number, pageSize: number) => {
    const newParams = { ...queryParams, current: page, pageSize };
    setQueryParams(newParams);
    fetchUserList(newParams);
  };

  // 编辑按钮点击
  const handleEdit = (record: UserData) => {
    // 转换数据格式以适配编辑模态框
    const editData = {
      id: record.id,
      userAccount: record.userAccount, // 账号名称
      userName: record.userName, // 使用人姓名
      department: record.department, // 科室
      position: record.position, // 职位
      tel: record.tel, // 联系方式
      enabled: record.status === 1, // 启用状态
      userRole: record.userRole, // 角色
      userAvatar: record.userAvatar, // 头像链接（如有）
    };
    setEditUser(editData);
    setEditModalVisible(true);
  };

  // 重置密码按钮点击
  const handleResetPwd = (record: UserData) => {
    setResetPwdAccount(record.userAccount || '');
    setResetPwdId(record.id);
    setResetPwdModalVisible(true);
  };

  // 扩展表格列，替换操作列
  const columns = [
    {
      title: '账号名称',
      dataIndex: 'userAccount',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: '使用科室',
      dataIndex: 'department',
    },
    {
      title: '使用人',
      dataIndex: 'userName',
    },
    {
      title: '职位',
      dataIndex: 'position',
    },
    {
      title: '联系方式',
      dataIndex: 'tel',
    },
    {
      title: '最后登录时间',
      dataIndex: 'lastLogin',
    },
    {
      title: '账号状态',
      dataIndex: 'status',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];
  const tableColumns = columns.map((col) => {
    if (col.dataIndex === 'action') {
      return {
        ...col,
        render: (_: any, record: UserData) => (
          <Space size={8} className="system-action">
            <a className="system-action-reset" onClick={() => handleResetPwd(record)}>
              重置密码
            </a>
            <a className="system-action-edit" onClick={() => handleEdit(record)}>
              编辑
            </a>
            <a
              className="system-action-delete"
              onClick={() => {
                Modal.confirm({
                  title: '确认删除该账号吗？',
                  content: '此操作不可恢复，是否继续？',
                  okText: '确认',
                  cancelText: '取消',
                  onOk: () => handleDelete(record),
                });
              }}
            >
              删除
            </a>
          </Space>
        ),
      };
    }
    if (col.dataIndex === 'status') {
      return {
        ...col,
        render: (status: number, record: UserData) => (
          <span className="system-status">
            <Switch
              checked={status === 1}
              size="small"
              className="system-status-switch"
              onChange={(checked) => handleStatusChange(record, checked)}
            />
            <span className={status === 1 ? 'system-status-enabled' : 'system-status-disabled'}>{status === 1 ? '启用' : '禁用'}</span>
          </span>
        ),
      };
    }
    return col;
  });

  // 转换数据格式以适配表格
  const tableData = userList.map((user) => ({
    id: user.id,
    key: user.id || 0,
    userAccount: user.userAccount || '',
    department: user.department || '-',
    userName: user.userName || '',
    position: user.position || '-',
    tel: user.tel || '-',
    lastLogin: user.lastLogin || '-',
    status: user.status !== undefined && user.status !== null ? user.status : 1,
  }));

  // rowSelection 配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    preserveSelectedRowKeys: true, // 支持跨页选中
  };

  return (
    <div className="user-table-section">
      {/* 筛选区 */}
      <div className="system-filter">
        <Space size={16} align="center" className="system-filter-row">
          <div className="system-filter-item">
            <span className="system-filter-item-label">账号状态：</span>
            <Select value={filters.status} onChange={(value) => setFilters((prev) => ({ ...prev, status: value }))}>
              <Option value="全部">全部</Option>
              <Option value="启用">启用</Option>
              <Option value="禁用">禁用</Option>
            </Select>
          </div>
          <div className="system-filter-item">
            <span className="system-filter-item-label">账号名称：</span>
            <Input
              placeholder="请输入账号名称"
              value={filters.userName}
              onChange={(e) => setFilters((prev) => ({ ...prev, userName: e.target.value }))}
            />
          </div>
          <div className="system-filter-btns">
            <Button className="system-filter-reset" onClick={handleReset}>
              重置
            </Button>
            <Button type="primary" className="system-filter-search" onClick={handleSearch}>
              查询
            </Button>
          </div>
        </Space>
      </div>
      {/* 用户表格 */}
      <div className="system-table-wrapper">
        {/* 操作按钮 */}
        <div className="system-operations">
          <Space>
            <Button type="primary" className="system-operations-create" onClick={() => setAddModalVisible(true)}>
              新建账号
            </Button>
            <Button className="system-operations-batch-create" onClick={() => setBatchAddModalVisible(true)}>
              批量新建
            </Button>
            <Button
              danger
              className="system-operations-delete"
              onClick={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
              type={selectedRowKeys.length > 0 ? 'primary' : 'default'}
            >
              删除账号
            </Button>
          </Space>
        </div>
        <Table
          rowSelection={rowSelection}
          columns={tableColumns}
          dataSource={tableData}
          pagination={false}
          bordered={false}
          loading={loading}
          scroll={{ x: 1200, y: 'calc(100vh - 430px)' }}
        />
        {/* 新建账号弹窗 */}
        <AddUserModal visible={addModalVisible} onCancel={() => setAddModalVisible(false)} onOk={() => refreshUserList(false, 0)} />
        {/* 批量新建账号弹窗 */}
        <BatchAddUserModal
          visible={batchAddModalVisible}
          onCancel={() => setBatchAddModalVisible(false)}
          onOk={() => refreshUserList(false, 0)}
        />
        {/* 编辑账号弹窗 */}
        <EditUserModal
          visible={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          onOk={() => refreshUserList(false, 0)}
          initialValues={editUser}
          onResetPassword={(username) => {
            setEditModalVisible(false);
            setResetPwdAccount(username);
            setResetPwdModalVisible(true);
          }}
        />
        {/* 重置密码弹窗 */}
        <ResetPasswordModal
          visible={resetPwdModalVisible}
          onCancel={() => {
            setResetPwdModalVisible(false);
            setResetPwdId(undefined);
          }}
          onOk={() => refreshUserList(false, 0)}
          accountName={resetPwdAccount}
          id={resetPwdId}
        />
        {/* 分页 */}
        <div className="system-pagination-bar">
          <Pagination
            current={queryParams.current}
            total={total}
            pageSize={queryParams.pageSize}
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={(total) => `共 ${total} 条`}
            onChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  );
};

export default UserTableSection;
