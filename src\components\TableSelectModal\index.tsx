import React, { useState, useEffect, useRef } from 'react';
import { Modal, message, Input, Button, Checkbox, Radio } from 'antd';
import * as XLSX from 'xlsx';
import Luckysheet from '@/components/Luckysheet';
import './index.less';

interface TableSelectModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedData: any[][]) => void;
}

const TableSelectModal: React.FC<TableSelectModalProps> = ({ visible, onCancel, onOk }) => {
  const [searchText, setSearchText] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [luckysheetData, setLuckysheetData] = useState<any[][]>([]);
  const [selectedRange, setSelectedRange] = useState<any>(null);
  const [selectedData, setSelectedData] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'all' | 'selected'>('all');

  useEffect(() => {
    if (visible) {
      fetch('https://alioss.chatexcel.com/data/【ChatExcel】交叉分析-关联分析-相关性分析-线性回归-分省生产总值年度数据V1.xlsx')
        .then((response) => response.arrayBuffer())
        .then((buffer) => {
          const workbook = XLSX.read(buffer, { type: 'array' });
          const sheet = workbook.Sheets[workbook.SheetNames[0]];
          const range = XLSX.utils.decode_range(sheet['!ref'] || 'A1');
          const raw: any[][] = [];

          for (let R = range.s.r; R <= range.e.r; ++R) {
            const row: any[] = [];
            for (let C = range.s.c; C <= range.e.c; ++C) {
              const cellRef = XLSX.utils.encode_cell({ c: C, r: R });
              const cell = sheet[cellRef];
              row.push(cell ? cell.v : '');
            }
            raw.push(row);
          }

          setLuckysheetData(raw);
        })
        .catch((err) => {
          message.error('Excel 加载失败：' + err.message);
        });
    }
  }, [visible]);

  // 添加检查区域是否全选的辅助函数
  const isFullRangeSelected = (range: any) => {
    if (!range || !luckysheetData.length) return false;

    const rowCount = luckysheetData.length;
    const colCount = luckysheetData[0]?.length || 0;

    return (
      range.row &&
      range.column &&
      range.row[0] === 0 &&
      range.row[1] === rowCount - 1 &&
      range.column[0] === 0 &&
      range.column[1] === colCount - 1
    );
  };

  useEffect(() => {
    // 确保数据加载完成后，初始化selectAll状态
    if (luckysheetData.length > 0 && selectedRange) {
      setSelectAll(isFullRangeSelected(selectedRange));
    }
  }, [luckysheetData, selectedRange]);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);

    // @ts-ignore
    const luckysheet = window.luckysheet;
    if (!luckysheet) return;

    if (checked && luckysheetData.length > 0) {
      // 获取当前工作表数据的实际范围
      const rowCount = luckysheetData.length;
      const colCount = luckysheetData[0]?.length || 0;

      // 选择所有单元格
      try {
        if (typeof luckysheet.setRangeSelect === 'function') {
          luckysheet.setRangeSelect({
            row: [0, rowCount - 1],
            column: [0, colCount - 1],
          });
        } else if (typeof luckysheet.setRangeShow === 'function') {
          luckysheet.setRangeShow({
            row: [0, rowCount - 1],
            column: [0, colCount - 1],
            rangeType: 'range',
          });
        }

        // 更新selectedRange状态
        const fullRange = {
          row: [0, rowCount - 1],
          column: [0, colCount - 1],
        };
        setSelectedRange(fullRange);
      } catch (error) {
        console.error('选择所有单元格失败:', error);
      }
    } else {
      // 取消选择状态
      setSelectedRange(null);

      try {
        if (typeof luckysheet.clearSelection === 'function') {
          luckysheet.clearSelection();
        } else if (typeof luckysheet.setRangeShow === 'function') {
          // 清除选区
          luckysheet.setRangeShow({
            row: [0, 0],
            column: [0, 0],
            rangeType: 'range',
            borderType: 'none',
          });
        }
      } catch (error) {
        console.error('清除选择失败:', error);
      }
    }
  };

  const handleRangeSelect = (range: any) => {
    setSelectedData(range.data);
    setSelectAll(isFullRangeSelected(range));
  };

  const handleDelete = () => {
    setSelectedData([]);
    setViewMode('all');
  };

  const handleOk = () => {
    if (isFullRangeSelected(selectedRange)) {
      setSelectedData(selectedData);
      onOk(selectedData);
    } else {
      if (!luckysheetData.length) {
        message.warning('请先选择一部分数据');
        return;
      }

      console.log('当前range值:', selectedRange);
      console.log('luckysheetData:', luckysheetData);

      // 提取选中区域的数据
      const selectedData: any[][] = [];

      try {
        if (selectedRange && luckysheetData.length > 0) {
          // 获取选中的行列范围
          const startRow = selectedRange.row?.[0] || 0;
          const endRow = selectedRange.row?.[1] || 0;
          const startCol = selectedRange.column?.[0] || 0;
          const endCol = selectedRange.column?.[1] || 0;

          // 提取所选区域的数据
          for (let i = startRow; i <= endRow; i++) {
            if (i < luckysheetData.length) {
              const rowData = luckysheetData[i];
              if (rowData) {
                const selectedRowData = rowData
                  .slice(startCol, endCol + 1)
                  .filter((cell) => cell !== undefined && cell !== null && cell !== '');
                if (selectedRowData.length > 0) {
                  selectedData.push(selectedRowData);
                }
              }
            }
          }
        } else {
          // 如果没有选择区域，但需要一些默认数据
          // 最多取前5行数据
          const maxRows = Math.min(5, luckysheetData.length);
          for (let i = 0; i < maxRows; i++) {
            const row = luckysheetData[i];
            if (row && Array.isArray(row)) {
              const validCells = row.filter((cell) => cell !== undefined && cell !== null && cell !== '');
              if (validCells.length > 0) {
                selectedData.push(validCells);
              }
            }
          }
        }
      } catch (error) {
        console.error('处理数据时出错:', error);
        message.error('处理数据时出错');
      }

      console.log('已选择的数据:', selectedData);

      if (selectedData.length === 0) {
        message.warning('未能获取有效的数据');
        return;
      }
      setSelectedData(selectedData);
      onOk(selectedData);
    }
    console.log(selectedData);
  };

  return (
    <Modal
      title="选择数据"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      width={1125}
      zIndex={1001}
      className="table-select-modal common-modal"
    >
      <div className="search-area">
        <Radio.Group value={viewMode} onChange={(e) => setViewMode(e.target.value)}>
          <Radio.Button value="all">全部数据</Radio.Button>
          {selectedData?.length > 0 && <Radio.Button value="selected">已选择的数据</Radio.Button>}
        </Radio.Group>
        <div className="operation-area">
          <Input.Search
            placeholder="请输入搜索内容"
            className="search-input"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
          {viewMode === 'all' && (
            <Checkbox checked={selectAll} onChange={(e) => handleSelectAll(e.target.checked)}>
              全选
            </Checkbox>
          )}
          {viewMode === 'selected' && (
            <Button type="primary" danger onClick={handleDelete}>
              删除
            </Button>
          )}
          {/* <Button type="primary" className="select-btn" onClick={handleOk}>
            选择数据
          </Button> */}
        </div>
      </div>

      <div style={{ height: '500px', width: '100%' }}>
        <Luckysheet
          data={viewMode === 'all' ? luckysheetData : selectedData}
          onRangeSelect={viewMode === 'all' ? handleRangeSelect : () => {}}
        />
      </div>
    </Modal>
  );
};

export default TableSelectModal;
