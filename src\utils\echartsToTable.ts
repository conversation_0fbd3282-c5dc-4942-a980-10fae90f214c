interface EChartsDatasetOption {
  source: Record<string, any>[]; // 每一行为一个数据项
  dimensions?: string[]; // 可选，用于明确列顺序
}

interface EChartsOptionWithDataset {
  dataset?: EChartsDatasetOption;
}

// 传统 ECharts 配置格式接口
interface EChartsSeriesOption {
  series?: Array<{
    name?: string;
    type?: string;
    data?: any[];
    [key: string]: any;
  }>;
  xAxis?:
    | {
        type?: string;
        data?: any[];
        [key: string]: any;
      }
    | Array<{
        type?: string;
        data?: any[];
        [key: string]: any;
      }>;
  yAxis?:
    | {
        type?: string;
        data?: any[];
        [key: string]: any;
      }
    | Array<{
        type?: string;
        data?: any[];
        [key: string]: any;
      }>;
  [key: string]: any;
}

interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
}

interface TableDataSourceItem {
  key: string;
  [key: string]: any;
}

interface TableResult {
  columns: TableColumn[];
  dataSource: TableDataSourceItem[];
}

/**
 * 从使用 dataset 的 ECharts 配置中提取表格数据
 * @param option ECharts 配置项（dataset 格式）
 * @param columnMap 自定义列标题映射（可选）
 */
export function extractTableFromOption(option: EChartsOptionWithDataset, columnMap?: Record<string, string>): TableResult {
  const source = option.dataset?.source;

  if (!Array.isArray(source) || source.length === 0) {
    return {
      columns: [],
      dataSource: [],
    };
  }

  const firstRow = source[0];
  const keys = Object.keys(firstRow);

  const columns: TableColumn[] = keys.map((key) => ({
    title: columnMap?.[key] || key,
    dataIndex: key,
    key,
  }));

  const dataSource: TableDataSourceItem[] = source.map((row, index) => ({
    key: String(index + 1),
    ...row,
  }));

  return {
    columns,
    dataSource,
  };
}

/**
 * 从传统 ECharts 配置中提取表格数据
 * @param option ECharts 配置项（传统格式，包含 series、xAxis、yAxis）
 * @param columnMap 自定义列标题映射（可选）
 */
export function extractTableFromSeriesOption(option: EChartsSeriesOption, columnMap?: Record<string, string>): TableResult {
  try {
    if (!option.series || !Array.isArray(option.series) || option.series.length === 0) {
      return {
        columns: [],
        dataSource: [],
      };
    }
  } catch (error) {
    console.error('提取表格数据时出错:', error);
    return {
      columns: [],
      dataSource: [],
    };
  }

  // 获取 x 轴数据作为行标识
  let xAxisData: any[] = [];
  if (option.xAxis) {
    const xAxis = Array.isArray(option.xAxis) ? option.xAxis[0] : option.xAxis;
    xAxisData = xAxis.data || [];
  }

  // 获取 y 轴数据作为行标识（适用于条形图等）
  let yAxisData: any[] = [];
  if (option.yAxis) {
    const yAxis = Array.isArray(option.yAxis) ? option.yAxis[0] : option.yAxis;
    if (yAxis.type === 'category' && yAxis.data) {
      yAxisData = yAxis.data;
    }
  }

  // 确定行标识数据
  const rowLabels = yAxisData.length > 0 ? yAxisData : xAxisData;

  // 处理不同类型的图表
  const firstSeries = option.series[0];
  const chartType = firstSeries.type;

  switch (chartType) {
    case 'pie':
    case 'funnel':
      return extractTableFromPieOrFunnel(option.series, columnMap);

    case 'wordCloud':
      return extractTableFromWordCloud(option.series, columnMap);

    default:
      return extractTableFromCartesian(option.series, rowLabels, columnMap);
  }
}

/**
 * 从饼图或漏斗图数据中提取表格数据
 */
function extractTableFromPieOrFunnel(series: any[], columnMap?: Record<string, string>): TableResult {
  try {
    if (!series || series.length === 0 || !series[0].data) {
      return { columns: [], dataSource: [] };
    }

    const data = series[0].data;
    const seriesName = series[0].name || '数值';

    const columns: TableColumn[] = [
      {
        title: columnMap?.['name'] || '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: columnMap?.[seriesName] || seriesName,
        dataIndex: 'value',
        key: 'value',
      },
    ];

    const dataSource: TableDataSourceItem[] = data.map((item: any, index: number) => {
      let name = `项目${index + 1}`;
      let value = 0;

      if (typeof item === 'object' && item !== null) {
        name = item.name || name;
        value = typeof item.value === 'number' ? item.value : 0;
      } else if (typeof item === 'number') {
        value = item;
      }

      return {
        key: String(index + 1),
        name,
        value,
      };
    });

    return { columns, dataSource };
  } catch (error) {
    console.error('提取饼图/漏斗图表格数据时出错:', error);
    return { columns: [], dataSource: [] };
  }
}

/**
 * 从词云图数据中提取表格数据
 */
function extractTableFromWordCloud(series: any[], columnMap?: Record<string, string>): TableResult {
  try {
    if (!series || series.length === 0 || !series[0].data) {
      return { columns: [], dataSource: [] };
    }

    const data = series[0].data;

    const columns: TableColumn[] = [
      {
        title: columnMap?.['name'] || '词语',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: columnMap?.['value'] || '权重',
        dataIndex: 'value',
        key: 'value',
      },
    ];

    const dataSource: TableDataSourceItem[] = data.map((item: any, index: number) => {
      let name = String(item);
      let value = 1;

      if (typeof item === 'object' && item !== null) {
        name = item.name || `词语${index + 1}`;
        value = typeof item.value === 'number' ? item.value : 1;
      }

      return {
        key: String(index + 1),
        name,
        value,
      };
    });

    return { columns, dataSource };
  } catch (error) {
    console.error('提取词云图表格数据时出错:', error);
    return { columns: [], dataSource: [] };
  }
}

/**
 * 从直角坐标系图表（柱状图、折线图、散点图等）数据中提取表格数据
 */
function extractTableFromCartesian(series: any[], rowLabels: any[], columnMap?: Record<string, string>): TableResult {
  try {
    if (!series || series.length === 0) {
      return { columns: [], dataSource: [] };
    }
  } catch (error) {
    console.error('提取直角坐标系图表表格数据时出错:', error);
    return { columns: [], dataSource: [] };
  }

  // 构建列定义
  const columns: TableColumn[] = [];

  // 添加行标识列（通常是 x 轴或 y 轴的分类数据）
  if (rowLabels.length > 0) {
    columns.push({
      title: columnMap?.['category'] || '分类',
      dataIndex: 'category',
      key: 'category',
    });
  }

  // 为每个系列添加一列
  series.forEach((s, index) => {
    const seriesName = s.name || `系列${index + 1}`;
    columns.push({
      title: columnMap?.[seriesName] || seriesName,
      dataIndex: `series_${index}`,
      key: `series_${index}`,
    });
  });

  // 构建数据源
  const dataSource: TableDataSourceItem[] = [];

  // 确定数据行数
  const maxDataLength = Math.max(rowLabels.length, ...series.map((s) => (s.data ? s.data.length : 0)));

  for (let i = 0; i < maxDataLength; i++) {
    const row: TableDataSourceItem = {
      key: String(i + 1),
    };

    // 添加分类数据
    if (rowLabels.length > 0) {
      row.category = rowLabels[i] || '';
    }

    // 添加各系列数据
    series.forEach((s, seriesIndex) => {
      if (s.data && s.data[i] !== undefined) {
        const dataItem = s.data[i];
        // 处理不同格式的数据
        if (typeof dataItem === 'object' && dataItem !== null) {
          // 散点图等可能是 [x, y] 格式
          if (Array.isArray(dataItem)) {
            row[`series_${seriesIndex}`] = dataItem.join(', ');
          } else if ('value' in dataItem) {
            // 有些数据可能是 {value: number, ...} 格式
            row[`series_${seriesIndex}`] = dataItem.value;
          } else {
            row[`series_${seriesIndex}`] = JSON.stringify(dataItem);
          }
        } else {
          row[`series_${seriesIndex}`] = dataItem;
        }
      } else {
        row[`series_${seriesIndex}`] = '';
      }
    });

    dataSource.push(row);
  }

  return { columns, dataSource };
}

/**
 * 智能提取表格数据 - 自动判断格式并选择合适的提取方法
 * @param option ECharts 配置项
 * @param columnMap 自定义列标题映射（可选）
 */
export function extractTableFromEChartsOption(option: any, columnMap?: Record<string, string>): TableResult {
  try {
    // 检查输入参数
    if (!option || typeof option !== 'object') {
      return {
        columns: [],
        dataSource: [],
      };
    }

    // 优先尝试 dataset 格式
    if (option.dataset?.source) {
      return extractTableFromOption(option, columnMap);
    }

    // 使用传统格式
    if (option.series) {
      return extractTableFromSeriesOption(option, columnMap);
    }

    // 都不匹配，返回空结果
    return {
      columns: [],
      dataSource: [],
    };
  } catch (error) {
    console.error('智能提取表格数据时出错:', error);
    return {
      columns: [],
      dataSource: [],
    };
  }
}
