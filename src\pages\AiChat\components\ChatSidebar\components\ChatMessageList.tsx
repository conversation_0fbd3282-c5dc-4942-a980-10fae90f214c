import React, { useRef, useEffect } from 'react';
import { Button, message } from 'antd';
import { CopyOutlined, RollbackOutlined, DeleteOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeRaw from 'rehype-raw';
import { isGuider, copyToClipboard } from '../utils';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  echart_option?: any;
  uploaded_files?: string[];
}

interface ChatMessageListProps {
  messages: Message[];
  processTip?: string;
  selectedAnalyst?: any;
  onResend: (content: string) => void;
  onDeleteMessage: (index: number) => void;
}

const ChatMessageList: React.FC<ChatMessageListProps> = ({ messages, processTip, selectedAnalyst, onResend, onDeleteMessage }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 处理复制功能
  const handleCopy = async (content: string) => {
    const result = await copyToClipboard(content);
    if (result.success) {
      message.success('复制成功');
    } else {
      message.error('复制失败');
    }
  };

  // 渲染单个消息
  const renderMessage = (msg: Message, index: number) => {
    if (!msg.content) {
      return null;
    }

    return (
      <div key={index} className={`message ${msg.role === 'user' ? 'user-message' : 'bot-message'}`}>
        <div className="message-timestamp">
          {msg.role === 'assistant' && (
            <div className="message-icon">
              <img src="/assets/image_1752042073186_4ji1wl.svg" alt="" />
            </div>
          )}
          {msg.timestamp}
        </div>
        <div className="message-content">
          {msg.role === 'assistant' && msg.content === '...' ? (
            <div className="tip-indicator">
              <span>{processTip}请稍等...</span>
            </div>
          ) : (
            <div className="message-content-wrapper markdown-body">
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkBreaks]}
                rehypePlugins={[rehypeRaw]}
                children={msg.content.replace(/\\n/g, '\n')}
              />
              <div className="message-actions">
                <Button type="text" size="small" icon={<CopyOutlined />} onClick={() => handleCopy(msg.content)} title="复制" />
                {msg.role === 'user' && (
                  <Button type="text" size="small" icon={<RollbackOutlined />} onClick={() => onResend(msg.content)} title="重新发送" />
                )}
                <Button type="text" size="small" icon={<DeleteOutlined />} onClick={() => onDeleteMessage(index)} title="删除" />
              </div>
            </div>
          )}
        </div>
        {msg.role === 'assistant' && msg.content === '...' && (
          <div className="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="chat-content">
      {messages.length === 0 ? (
        <>
          {selectedAnalyst ? (
            isGuider(selectedAnalyst) ? (
              <div key="welcome" className="welcome-message">
                <div className="welcome-icon">
                  <img src="/assets/image_1752042073186_4ji1wl.svg" alt="" />
                </div>
                <div className="welcome-text">hi,我是深度问数,我可以根据你的问题,分析数据生成图表</div>
              </div>
            ) : (
              // 用 selectedAnalyst 替换原有分析师类型映射
              <h4 key="analyst-type">您的{selectedAnalyst?.assistantName || '默认分析师'}与您对话</h4>
            )
          ) : null}
        </>
      ) : (
        <>{messages.map((msg, index) => renderMessage(msg, index))}</>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessageList;
