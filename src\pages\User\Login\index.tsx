import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Checkbox } from 'antd';
import { history, useModel } from '@umijs/max';
import { flushSync } from 'react-dom';
import { getLoginUser, userLogin } from '@/services/DataLoom/userController';
import Settings from '../../../../config/defaultSettings';
import LegalLinks from '@/components/LegalLinks';
import './index.less';

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { setInitialState } = useModel('@@initialState');

  // 组件加载时从localStorage获取保存的用户名和密码
  useEffect(() => {
    const savedUsername = localStorage.getItem('remembered_username');
    const savedPassword = localStorage.getItem('remembered_password');
    if (savedUsername && savedPassword) {
      form.setFieldsValue({
        userAccount: savedUsername,
        userPassword: savedPassword,
        userRemember: true,
      });
    }
  }, [form]);

  const fetchUserInfo = async () => {
    try {
      const userInfo = await getLoginUser();
      if (userInfo) {
        flushSync(() => {
          setInitialState({
            currentUser: userInfo.data,
            settings: Settings,
          });
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败，请刷新页面重试');
    }
  };

  const handleSubmit = async (values: API.UserLoginRequest) => {
    setLoading(true);
    try {
      const res = await userLogin({
        ...values,
      });

      if (res.code === 0) {
        message.success('登录成功！');

        // 处理记住密码
        if (values.userRemember && values.userAccount && values.userPassword) {
          localStorage.setItem('remembered_username', values.userAccount);
          localStorage.setItem('remembered_password', values.userPassword);
        } else {
          localStorage.removeItem('remembered_username');
          localStorage.removeItem('remembered_password');
        }

        await fetchUserInfo();

        const urlParams = new URL(window.location.href).searchParams;
        history.push(urlParams.get('redirect') || '/');
      } else {
        message.error(res.message || '登录失败，请重试！');
      }
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-page">
      <div className="logo-container">
        <img src="/assets/iaayxt25.svg" alt="logo" />
      </div>

      <div className="login-container">
        <h2 className="login-title">登录账号</h2>

        <Form form={form} name="login" onFinish={handleSubmit} autoComplete="off" size="large">
          <Form.Item
            name="userAccount"
            rules={[
              { required: true, message: '请输入用户名' },
              { pattern: /^[a-z][a-z0-9\-]*[a-z0-9]$/, message: '用户名只能由小写字母、数字、-组成' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item name="userPassword" rules={[{ required: true, message: '请输入密码' }]}>
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <div style={{ margin: '28px 0 12px' }}>
            <Form.Item name="userRemember" valuePropName="checked" initialValue={true} noStyle>
              <Checkbox>记住密码</Checkbox>
            </Form.Item>
          </div>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block className="login-button">
              登录
            </Button>
          </Form.Item>
        </Form>

        <div className="legal-links-container">
          <LegalLinks theme="light" size="small" />
        </div>
      </div>
    </div>
  );
};

export default Login;
