import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, message, Table, Tabs, Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import Luckysheet from '@/components/Luckysheet';
import { useModel } from '@umijs/max';
import { downloadFile, getMysqlTableFields } from '@/services/DataLoom/fileController';
import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import './index.less';

// 自定义loading组件
const CustomLoading = () => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
    }}
  >
    <img
      src="/assets/5h1owxeu.png"
      alt="loading"
      style={{
        width: 163,
        height: 204,
      }}
    />
    <span
      style={{
        fontSize: '14px',
        color: '#666',
      }}
    >
      正在打开请稍等...
    </span>
  </div>
);

interface PreviewDrawerProps {
  visible: boolean;
  onClose: () => void;
  dataSource?: {
    id: string;
    name: string;
    type: string;
    description: string;
    configuration: string;
    tableNames?: string[];
  };
  selectedTables?: string[];
  onRemoveTable?: (tableName: string) => void;
}

const PreviewDrawer: React.FC<PreviewDrawerProps> = ({ visible, onClose, dataSource, selectedTables, onRemoveTable }) => {
  const [previewLoading, setPreviewLoading] = useState(false);
  const loadingStartTimeRef = useRef<number>(0);
  const MIN_LOADING_TIME = 3000;
  const [drawerOpened, setDrawerOpened] = useState(false);
  const [luckysheetData, setLuckysheetData] = useState<any[]>([]);
  const [tableFields, setTableFields] = useState<Record<string, API.CoreDatasetTableField[]>>({});
  const [activeTab, setActiveTab] = useState<string>('');
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  // 定义表格列
  const columns = [
    {
      title: '字段名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '原始字段名',
      dataIndex: 'originName',
      key: 'originName',
    },
    {
      title: '字段类型',
      dataIndex: 'fieldType',
      key: 'fieldType',
    },
  ];
  // 处理标签页编辑事件
  const handleEdit = (
    targetKey: string | React.MouseEvent<Element, MouseEvent> | React.KeyboardEvent<Element>,
    action: 'add' | 'remove',
  ) => {
    if (action === 'remove' && typeof targetKey === 'string') {
      Modal.confirm({
        title: '确认关闭',
        content: `确定要关闭 ${targetKey} 标签页吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // 通知父组件移除
          onRemoveTable?.(targetKey);

          // 如果当前激活的标签页被移除，则激活第一个标签页
          if (activeTab === targetKey) {
            const remainingTabs = selectedTables?.filter((tab) => tab !== targetKey) || [];
            if (remainingTabs.length > 0) {
              setActiveTab(remainingTabs[0]);
            }
          }

          // 从 tableFields 中移除对应的表结构数据
          setTableFields((prev) => {
            const newFields = { ...prev };
            delete newFields[targetKey];
            return newFields;
          });
        },
      });
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!dataSource?.name) {
        message.error('文件名称不存在');
        return;
      }

      if (!currentUser?.id) {
        message.error('用户信息不存在');
        return;
      }

      loadingStartTimeRef.current = Date.now();

      try {
        const response = await downloadFile({
          fileName: dataSource.configuration,
          userId: currentUser.id.toString(),
        });
        const buffer = await response.arrayBuffer();

        // 根据文件扩展名判断文件类型
        const fileExtension = dataSource.configuration.split('.').pop()?.toLowerCase();
        let raw: string[][] = [];

        if (fileExtension === 'csv') {
          const text = new TextDecoder('utf-8').decode(buffer);
          if (!text || text.trim().length === 0) {
            throw new Error('文件内容为空');
          }

          // 移除UTF-8 BOM标记
          const cleanText = text.charCodeAt(0) === 0xfeff ? text.slice(1) : text;

          const result = Papa.parse<string[]>(cleanText, {
            header: false,
            skipEmptyLines: true,
            delimiter: ',', // 明确指定分隔符为英文逗号
            transform: (value: string) => value.trim(),
          });

          if (result.errors.length > 0) {
            throw new Error('CSV文件格式错误');
          }

          raw = result.data as string[][];
        } else if (fileExtension === 'xls' || fileExtension === 'xlsx') {
          // 使用 XLSX 解析 Excel 文件
          const workbook = XLSX.read(buffer, { type: 'array' });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];

          // 将工作表转换为二维数组
          raw = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

          if (!raw || raw.length === 0) {
            throw new Error('Excel文件内容为空');
          }
        } else {
          throw new Error('不支持的文件格式');
        }

        setLuckysheetData(raw);
      } catch (err) {
        message.error(`文件加载失败: ${err instanceof Error ? err.message : '未知错误'}`);
      }
    };

    if (drawerOpened && dataSource) {
      if (dataSource.type === 'mysql') {
        if (selectedTables && selectedTables.length > 0) {
          setActiveTab(selectedTables[0]);

          selectedTables.forEach((tableName) => {
            const params = {
              datasourceId: dataSource.id,
              tableName: tableName,
            };
            getMysqlTableFields(params)
              .then((res) => {
                if (res?.data) {
                  setTableFields((prev) => ({
                    ...prev,
                    [tableName]: res.data,
                  }));
                }
              })
              .catch((err) => {
                message.error(`获取表 ${tableName} 结构失败: ${err.message}`);
              });
          });
        }
      } else {
        fetchData();
      }
    }
  }, [drawerOpened, dataSource, currentUser?.id, selectedTables]);

  // 处理loading状态
  const handleLoadingComplete = () => {
    const currentTime = Date.now();
    const elapsedTime = currentTime - loadingStartTimeRef.current;

    if (elapsedTime >= MIN_LOADING_TIME) {
      setPreviewLoading(false);
    } else {
      const remainingTime = MIN_LOADING_TIME - elapsedTime;
      setTimeout(() => {
        setPreviewLoading(false);
      }, remainingTime);
    }
  };

  const renderContent = () => {
    if (dataSource?.type === 'mysql' && selectedTables && selectedTables.length > 0) {
      return (
        <Tabs
          hideAdd
          type="editable-card"
          onEdit={handleEdit}
          activeKey={activeTab}
          onChange={setActiveTab}
          items={selectedTables.map((tableName) => ({
            key: tableName,
            label: tableName,
            children: (
              <Table
                columns={columns}
                dataSource={tableFields[tableName] || []}
                rowKey={(record) => record.id || record.name || Math.random().toString()}
                pagination={false}
                size="small"
                bordered
              />
            ),
          }))}
        />
      );
    }
    return <Luckysheet data={luckysheetData} />;
  };

  return (
    <Drawer
      title="数据预览"
      placement="right"
      width="50%"
      getContainer={false}
      className="mysql-detail-preview-drawer"
      onClose={onClose}
      open={visible}
      destroyOnHidden
      afterOpenChange={(open) => setDrawerOpened(open)}
    >
      <div className="preview-content">
        <Spin spinning={previewLoading} indicator={<CustomLoading />}>
          {renderContent()}
        </Spin>
      </div>
    </Drawer>
  );
};

export default PreviewDrawer;
