import React, { useEffect, useRef } from 'react';
import './index.less';

declare global {
  interface Window {
    luckysheet: any;
  }
}

interface LuckysheetProps {
  data?: any[][];
  onDataChange?: (data: any[][]) => void;
  onRangeSelect?: (range: any) => void;
}

const Luckysheet: React.FC<LuckysheetProps> = ({ data, onDataChange, onRangeSelect }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      // 清除旧表格（重复打开可能导致多个 luckysheet 重叠）
      containerRef.current.innerHTML = '';

      window.luckysheet?.create({
        container: 'luckysheet',
        lang: 'zh',
        title: '数据预览',
        showinfobar: false,
        showtoolbar: true,
        showtoolbarConfig: {
          undoRedo: true, //撤销重做
          paintFormat: true, //格式刷
          font: true, //字体
          fontSize: true, //字号大小
          bold: true, //粗体
          italic: true, //斜体
          underline: true, //下划线
          textColor: true, //文本颜色
          fillColor: true, //单元格颜色
          border: true, //边框
          mergeCell: true, //合并单元格
          horizontalAlignMode: true, //水平对齐方式
          verticalAlignMode: true, //垂直对齐方式
          currencyFormat: false, //货币格式
          percentageFormat: false, //百分比格式
          numberDecrease: false, //减少小数位数
          numberIncrease: false, //增加小数位数
          moreFormats: false, //更多格式
          strikethrough: false, //删除线
          textWrapMode: false, //换行方式
          textRotateMode: false, //文本旋转方式
          image: false, //插入图片
          link: false, //插入链接
          chart: false, //图表
          postil: false, //批注
          pivotTable: false, //数据透视表
          function: false, //公式
          frozenMode: false, //冻结方式
          sortAndFilter: false, //排序和筛选
          conditionalFormat: false, //条件格式
          dataVerification: false, //数据验证
          splitColumn: false, //分列
          screenshot: false, //截图
          findAndReplace: false, //查找替换
          protection: false, //工作表保护
          print: false, //打印
        },
        data: [
          {
            name: 'Sheet1',
            index: 0,
            status: 1,
            order: 0,
            color: '',
            row: data?.length || 100,
            column: data?.[0]?.length || 100,
            data:
              data ||
              Array.from({ length: 100 }, (_, rowIndex) => {
                return Array.from({ length: 100 }, (_, colIndex) => {
                  return `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
                });
              }),
            config: {},
          },
        ],
        hook: {
          updated: (data: any) => {
            onDataChange?.(data);
          },
          rangeSelect: (range: any) => {
            // console.log('原始range:', range);

            // 确保我们有选择范围
            if (!range || !range.row || !range.column) {
              console.log('无效的range对象');
              onRangeSelect?.(null);
              return;
            }

            try {
              const currentSheet = window.luckysheet.getSheetData();
              if (!currentSheet) {
                console.log('无法获取当前表格数据');
                onRangeSelect?.(range);
                return;
              }

              const startRow = range.row[0];
              const endRow = range.row[1];
              const startCol = range.column[0];
              const endCol = range.column[1];

              // 直接使用luckysheet API获取选中的数据

              const selectedData = window.luckysheet.getRangeValue({ row: [startRow, endRow], column: [startCol, endCol] });

              // console.log('选中的数据:', selectedData);

              // 同时返回选中范围和数据
              onRangeSelect?.({
                range,
                data: selectedData,
              });
            } catch (error) {
              console.error('处理选中范围时出错:', error);
              // 如果出错，至少返回原始range对象
              onRangeSelect?.(range);
            }
          },
        },
      });
    }

    return () => {
      // 清理 luckysheet
      const moreBtnDiv = document.querySelector('#luckysheet-icon-morebtn-div');
      if (moreBtnDiv) {
        moreBtnDiv.remove();
      }
    };
  }, [data]);

  return <div ref={containerRef} id="luckysheet" style={{ position: 'relative', width: '100%', height: '100%' }} />;
};

export default Luckysheet;
