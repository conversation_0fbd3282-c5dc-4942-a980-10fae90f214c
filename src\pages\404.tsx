import { history } from '@umijs/max';
import { Button, Result } from 'antd';
import React from 'react';
import styles from './404.less';

const NoFoundPage: React.FC = () => (
  <div className={styles.container}>
    <Result
      status="404"
      title="404"
      subTitle={
        <div className={styles.subTitle}>
          <p>抱歉，您访问的页面不存在</p>
          <p className={styles.subText}>请检查您输入的网址是否正确，或者点击下方按钮返回首页</p>
        </div>
      }
      extra={
        <Button type="primary" size="large" className={styles.backButton} onClick={() => history.push('/')}>
          返回首页
        </Button>
      }
    />
  </div>
);

export default NoFoundPage;
