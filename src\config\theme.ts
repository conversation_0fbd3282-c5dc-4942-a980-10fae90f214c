export const colorOptions = [
  {
    value: 'theme1',
    colors: ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F', '#D95040'],
  },
  {
    value: 'theme2',
    colors: ['#D97559', '#E4C477', '#DAA67B', '#4E9C8F', '#4786B4', '#2E4552'],
  },
  {
    value: 'theme3',
    colors: ['#3F95C2', '#6DC8E1', '#4EB2D4', '#B8E6F2', '#A1DEED', '#D1EFF7'],
  },
  {
    value: 'theme4',
    colors: ['#2478F2', '#84B7F9', '#5F9CF8', '#CEE5FC', '#AAD0FB', '#E5F3FF'],
  },
  {
    value: 'theme5',
    colors: ['#F39423', '#F9C78B', '#F5A544', '#FBDAB2', '#F7B463', '#FDECD8'],
  },
  {
    value: 'theme6',
    colors: ['#42494F', '#999FA6', '#63686E', '#B3B9BF', '#7E858C', '#D1D5D9'],
  },
];

export const defaultTheme = 'theme1';

export const getThemeColors = (theme?: string) => {
  const themeConfig = colorOptions.find((option) => option.value === theme);
  return themeConfig?.colors || colorOptions[0].colors;
};
