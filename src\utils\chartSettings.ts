import type { ThemeConfigValues } from '@/components/ChartSettings';
import { getThemeColors as getConfigThemeColors } from '@/config/theme';

// 图表配置相关类型定义
export interface ChartOption {
  title?: any;
  tooltip?: any;
  legend?: any;
  grid?: any;
  xAxis?: any;
  yAxis?: any;
  series?: any[];
  color?: any;
  backgroundColor?: string;
  textStyle?: any;
  animation?: boolean;
  [key: string]: any;
}

/**
 * 获取主题颜色
 * @param theme 主题名称
 * @returns 主题颜色数组
 */
export const getThemeColors = (theme: string) => {
  return getConfigThemeColors(theme);
};

/**
 * 获取网格线类型
 * @param gridStyle 网格样式
 * @returns ECharts 网格线类型
 */
export const getGridLineType = (gridStyle: string) => {
  if (!gridStyle || typeof gridStyle !== 'string') {
    return 'solid';
  }

  const styleMap: { [key: string]: string } = {
    实线: 'solid',
    虚线: 'dashed',
    点线: 'dotted',
  };
  return styleMap[gridStyle] || 'solid';
};

/**
 * 格式化数值，应用前缀和后缀单位
 * @param value 原始值
 * @param settings 图表设置
 * @returns 格式化后的字符串
 */
export const formatValueWithUnit = (value: any, settings: ThemeConfigValues): string => {
  if (value === null || value === undefined) return '';

  let formattedValue = String(value);

  // 应用前缀单位
  if (settings.showPrefixUnit && settings.prefixUnitText) {
    formattedValue = settings.prefixUnitText + formattedValue;
  }

  // 应用后缀单位
  if (settings.showSuffixUnit && settings.suffixUnitText) {
    formattedValue = formattedValue + settings.suffixUnitText;
  }

  return formattedValue;
};

/**
 * 判断图表类型是否需要坐标轴
 * @param chartType 图表类型
 * @returns 是否需要坐标轴
 */
const shouldHaveAxis = (chartType?: string): boolean => {
  const noAxisChartTypes = ['pie', 'funnel', 'word-cloud'];
  return !noAxisChartTypes.includes(chartType || '');
};

/**
 * 判断图表类型是否需要网格
 * @param chartType 图表类型
 * @returns 是否需要网格
 */
const shouldHaveGrid = (chartType?: string): boolean => {
  const noGridChartTypes = ['pie', 'funnel', 'word-cloud'];
  return !noGridChartTypes.includes(chartType || '');
};

/**
 * 应用图表设置到图表配置
 * @param baseOption 基础图表配置
 * @param settings 图表设置
 * @param chartType 图表类型（可选）
 * @returns 应用设置后的图表配置
 */
export const applyChartSettings = (baseOption: any, settings: ThemeConfigValues, chartType?: string): any => {
  if (!baseOption || !settings) return baseOption;

  const updatedOption = { ...baseOption };

  // 应用主题颜色
  if (settings.theme && settings.theme !== 'default') {
    updatedOption.color = getThemeColors(settings.theme);
  }

  // 应用图例设置
  updatedOption.legend = {
    ...updatedOption.legend,
    show: settings.showLegend,
  };

  // 应用数据标签设置
  if (updatedOption.series) {
    updatedOption.series = updatedOption.series.map((series: any) => {
      const updatedSeries = {
        ...series,
        label: {
          ...series.label,
          show: settings.showDataLabel,
          fontSize: settings.showDataLabel ? parseInt(settings.dataLabelTextSize) || 12 : 12,
          formatter: (params: any) => {
            return formatValueWithUnit(params.value, settings);
          },
        },
      };

      // 应用柱状图宽度比例设置
      if (series.type === 'bar' && settings.widthRatio) {
        updatedSeries.barWidth = `${settings.widthRatio}%`;
      }

      return updatedSeries;
    });
  }

  // 应用坐标轴设置（只对需要坐标轴的图表类型应用）
  if (shouldHaveAxis(chartType)) {
    if (settings.showAxis) {
      // X轴设置
      if (updatedOption.xAxis) {
        updatedOption.xAxis = {
          ...updatedOption.xAxis,
          show: settings.showAxis,
          axisLine: {
            show: settings.showAxis,
          },
          axisTick: {
            show: settings.showAxis,
          },
          axisLabel: {
            ...updatedOption.xAxis.axisLabel,
            fontSize: parseInt(settings.textSize) || 12,
            color: settings.textColor,
            rotate: parseInt(settings.axisAngle?.replace('°', '') || '0'),
          },
          // X轴通常不需要设置数值范围，除非是数值型X轴
          ...(updatedOption.xAxis.type === 'value' && {
            max: settings.maxValue !== '自动' ? parseFloat(settings.maxValue) || null : null,
            min: settings.minValue !== '自动' ? parseFloat(settings.minValue) || null : null,
            interval: settings.dataInterval !== '自动' ? parseFloat(settings.dataInterval) || null : null,
          }),
        };
      }

      // Y轴设置
      if (updatedOption.yAxis) {
        updatedOption.yAxis = {
          ...updatedOption.yAxis,
          show: settings.showAxis,
          axisLine: {
            show: settings.showAxis,
          },
          axisTick: {
            show: settings.showAxis,
          },
          axisLabel: {
            ...updatedOption.yAxis.axisLabel,
            fontSize: parseInt(settings.textSize) || 12,
            color: settings.textColor,
          },
          max: settings.maxValue !== '自动' ? parseFloat(settings.maxValue) || null : null,
          min: settings.minValue !== '自动' ? parseFloat(settings.minValue) || null : null,
          interval: settings.dataInterval !== '自动' ? parseFloat(settings.dataInterval) || null : null,
        };
      }
    } else {
      // 如果不显示坐标轴，确保设置为隐藏
      if (updatedOption.xAxis) updatedOption.xAxis.show = false;
      if (updatedOption.yAxis) updatedOption.yAxis.show = false;
    }
  } else {
    // 对于不需要坐标轴的图表类型，确保隐藏坐标轴
    updatedOption.xAxis = { show: false };
    updatedOption.yAxis = { show: false };
  }

  // 应用网格设置（只对需要网格的图表类型应用）
  if (shouldHaveGrid(chartType)) {
    if (settings.showGrid) {
      // 设置网格线样式
      const gridLineStyle = {
        color: settings.gridColor,
        width: parseInt(settings.gridWidth) || 1,
        type: getGridLineType(settings.gridStyle),
      };

      if (updatedOption.xAxis) {
        updatedOption.xAxis.splitLine = {
          show: settings.showGrid,
          lineStyle: gridLineStyle,
        };
      }

      if (updatedOption.yAxis) {
        updatedOption.yAxis.splitLine = {
          show: settings.showGrid,
          lineStyle: gridLineStyle,
        };
      }
    } else {
      // 如果不显示网格，确保隐藏分割线
      if (updatedOption.xAxis) {
        updatedOption.xAxis.splitLine = { show: false };
      }
      if (updatedOption.yAxis) {
        updatedOption.yAxis.splitLine = { show: false };
      }
    }
  } else {
    // 对于不需要网格的图表类型，确保隐藏网格线
    if (updatedOption.xAxis) {
      updatedOption.xAxis.splitLine = { show: false };
    }
    if (updatedOption.yAxis) {
      updatedOption.yAxis.splitLine = { show: false };
    }
  }

  // 应用数据提示设置
  updatedOption.tooltip = {
    ...updatedOption.tooltip,
    show: settings.showDataTips,
    formatter: settings.showDataTips
      ? (params: any) => {
          let value = Array.isArray(params) ? params[0].value : params.value;
          let name = Array.isArray(params) ? params[0].name : params.name;

          const formattedValue = formatValueWithUnit(value, settings);
          if (!formattedValue) return '';

          return `${name}: ${formattedValue}`;
        }
      : undefined,
  };

  // 应用动画设置
  updatedOption.animation = settings.showAnimation;

  return updatedOption;
};
