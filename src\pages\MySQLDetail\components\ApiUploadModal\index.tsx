'use client';

import type React from 'react';
import { useState } from 'react';
import { Modal, Form, Input, Radio, Tabs, Select, Button, Space, Steps, InputNumber, DatePicker } from 'antd';
import { PlusOutlined, CloseOutlined, MinusCircleOutlined } from '@ant-design/icons';
import './index.less';
import moment from 'moment';

const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

interface DataSourceModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
}

const DataSourceModal: React.FC<DataSourceModalProps> = ({ visible, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const [apiModalVisible, setApiModalVisible] = useState(false);
  const [apiForm] = Form.useForm();
  const [activeApiTab, setActiveApiTab] = useState('1');
  const [updateFrequency, setUpdateFrequency] = useState('RIGHTNOW');
  const [updateType, setUpdateType] = useState('all_scope');

  // 处理主表单提交
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        onOk(values);
      })
      .catch((info) => {
        console.log('验证失败:', info);
      });
  };

  // 打开API连接弹窗
  const showApiModal = () => {
    setApiModalVisible(true);
  };

  // 关闭API连接弹窗
  const closeApiModal = () => {
    setApiModalVisible(false);
  };

  // 处理API表单下一步
  const handleApiNext = () => {
    apiForm
      .validateFields()
      .then((values) => {
        console.log('API表单数据:', values);
        setActiveApiTab('2');
      })
      .catch((info) => {
        console.log('验证失败:', info);
      });
  };

  const handleUpdateFrequencyChange = (e) => {
    setUpdateFrequency(e.target.value);
  };

  return (
    <>
      <Modal
        title="新增数据源"
        open={visible}
        onCancel={onCancel}
        onOk={handleSubmit}
        width={800}
        className="data-source-modal common-modal"
        closeIcon={<CloseOutlined />}
      >
        <Form form={form} layout="vertical" className="data-source-form">
          <Form.Item label="数据源名称" name="name" rules={[{ required: true, message: '请输入数据源名称' }]} className="form-item">
            <Input placeholder="请输入名称" />
          </Form.Item>

          <Form.Item label="数据源描述" name="description" className="form-item">
            <TextArea placeholder="描述数据集的来源、作用,可不填写" rows={4} />
          </Form.Item>

          <Form.Item label="添加数据表" name="dataTables" rules={[{ required: true, message: '请添加数据表' }]} className="form-item">
            <div className="data-table-container">
              <Button type="dashed" icon={<PlusOutlined />} className="add-table-button" onClick={showApiModal} />
            </div>
          </Form.Item>

          <div className="form-section-divider" />

          <div className="form-section-title">数据更新设置</div>

          <Form.Item
            label="更新方式"
            name="updateType"
            rules={[{ required: true, message: '请选择更新方式' }]}
            initialValue="all_scope"
            className="form-item"
          >
            <Radio.Group onChange={(e) => setUpdateType(e.target.value)} value={updateType}>
              <Radio value="all_scope">全量更新</Radio>
              <Radio value="add_scope">增量更新</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="更新频率"
            name="syncRate"
            rules={[{ required: true, message: '请选择更新频率' }]}
            initialValue="RIGHTNOW"
            className="form-item"
          >
            <Radio.Group onChange={handleUpdateFrequencyChange} value={updateFrequency}>
              <Radio value="RIGHTNOW">立即更新</Radio>
              <Radio value="CRON">表达式设定</Radio>
              <Radio value="SIMPLE_CRON">简单重复</Radio>
            </Radio.Group>
          </Form.Item>

          {updateFrequency === 'CRON' && (
            <>
              <Form.Item name="cron" label="CRON 表达式" rules={[{ required: true, message: '请输入CRON表达式' }]}>
                <Input placeholder="请输入CRON表达式" />
              </Form.Item>
              <Form.Item name="startTime" label="开始时间" rules={[{ required: true, message: '请选择开始时间' }]}>
                <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
              </Form.Item>
              <Form.Item name="endTime" label="结束时间">
                <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
              </Form.Item>
            </>
          )}

          {updateFrequency === 'SIMPLE_CRON' && (
            <>
              <Form.Item label="更新频率" required={true}>
                <Input.Group compact style={{ display: 'flex', alignItems: 'center' }}>
                  每
                  <Form.Item name="simpleCronValue" noStyle rules={[{ required: true, message: '请输入更新频率' }]}>
                    <InputNumber
                      style={{
                        width: '10%',
                        marginLeft: '8px',
                        marginRight: '8px',
                      }}
                      placeholder="每多少"
                    />
                  </Form.Item>
                  <Form.Item name="simpleCronType" noStyle rules={[{ required: true, message: '请选择更新频率单位' }]}>
                    <Select
                      style={{
                        width: '10%',
                        marginLeft: '8px',
                        marginRight: '8px',
                      }}
                    >
                      <Option value="second">秒</Option>
                      <Option value="minute">分钟</Option>
                      <Option value="hour">小时</Option>
                      <Option value="day">天</Option>
                      <Option value="week">周</Option>
                      <Option value="month">月</Option>
                    </Select>
                  </Form.Item>
                  更新一次
                </Input.Group>
              </Form.Item>
              <Form.Item name="startTime" label="开始时间" rules={[{ required: true, message: '请选择开始时间' }]}>
                <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
              </Form.Item>
              <Form.Item name="endTime" label="结束时间">
                <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* API连接弹窗 */}
      <Modal
        title="新增数据源"
        open={apiModalVisible}
        onCancel={closeApiModal}
        onOk={handleApiNext}
        width={700}
        className="api-connection-modal"
        closeIcon={<CloseOutlined />}
      >
        <Steps
          current={parseInt(activeApiTab) - 1}
          className="api-steps"
          items={[
            {
              title: '连接API',
            },
            {
              title: '提取数据',
            },
          ]}
        />

        <Tabs activeKey={activeApiTab} onChange={setActiveApiTab} className="api-tabs" renderTabBar={() => <></>}>
          <TabPane key="1">
            <Form
              form={apiForm}
              layout="vertical"
              initialValues={{
                method: 'GET',
                bodyType: 'formData',
              }}
            >
              <Form.Item label="数据表名称" name="name" rules={[{ required: true, message: '请输入数据表名称' }]} className="form-item">
                <Input />
              </Form.Item>

              <Form.Item label="请求" required={true}>
                <Input.Group compact>
                  <Form.Item noStyle name="method">
                    <Select style={{ width: '20%' }} defaultValue="GET">
                      <Option value="GET">GET</Option>
                      <Option value="POST">POST</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item noStyle rules={[{ required: true, message: '请输入请求URL' }]} name="url">
                    <Input style={{ width: '80%' }} placeholder="请输入请求URL" />
                  </Form.Item>
                </Input.Group>
              </Form.Item>

              <Form.Item className="form-item">
                <Tabs defaultActiveKey="headers" className="request-tabs">
                  <TabPane tab="请求头" key="headers">
                    <Form.List name={['request', 'headers']}>
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                              <Form.Item {...restField} name={[name, 'key']} rules={[{ required: true, message: '请输入键' }]}>
                                <Input placeholder="键" />
                              </Form.Item>
                              <Form.Item {...restField} name={[name, 'value']} rules={[{ required: true, message: '请输入值' }]}>
                                <Input placeholder="值" />
                              </Form.Item>
                              <MinusCircleOutlined onClick={() => remove(name)} />
                            </Space>
                          ))}
                          <Form.Item>
                            <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                              添加参数
                            </Button>
                          </Form.Item>
                        </>
                      )}
                    </Form.List>
                  </TabPane>
                  <TabPane tab="QUERY参数" key="query">
                    <Form.List name={['request', 'arguments']}>
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                              <Form.Item {...restField} name={[name, 'key']} rules={[{ required: true, message: '请输入键' }]}>
                                <Input placeholder="键" />
                              </Form.Item>
                              <Form.Item {...restField} name={[name, 'value']} rules={[{ required: true, message: '请输入值' }]}>
                                <Input placeholder="值" />
                              </Form.Item>
                              <MinusCircleOutlined onClick={() => remove(name)} />
                            </Space>
                          ))}
                          <Form.Item>
                            <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                              添加参数
                            </Button>
                          </Form.Item>
                        </>
                      )}
                    </Form.List>
                  </TabPane>
                  <TabPane tab="请求体" key="body">
                    <Form.Item name={['request', 'bodyType']} label="请求体类型">
                      <Radio.Group>
                        <Radio value="formData">Form Data</Radio>
                        <Radio value="urlEncoded">x-www-form-urlencoded</Radio>
                        <Radio value="raw">Raw</Radio>
                        <Radio value="xml">XML</Radio>
                        <Radio value="json">JSON</Radio>
                      </Radio.Group>
                    </Form.Item>

                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) => prevValues.request?.bodyType !== currentValues.request?.bodyType}
                    >
                      {({ getFieldValue }) => {
                        const bodyType = getFieldValue(['request', 'bodyType']);

                        if (bodyType === 'formData') {
                          return (
                            <Form.List name={['request', 'body', 'formData']}>
                              {(fields, { add, remove }) => (
                                <>
                                  {fields.map(({ key, name, ...restField }) => (
                                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                                      <Form.Item {...restField} name={[name, 'formKey']} rules={[{ required: true, message: '请输入键' }]}>
                                        <Input placeholder="键" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'formValue']}
                                        rules={[{ required: true, message: '请输入值' }]}
                                      >
                                        <Input placeholder="值" />
                                      </Form.Item>
                                      <MinusCircleOutlined onClick={() => remove(name)} />
                                    </Space>
                                  ))}
                                  <Form.Item>
                                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                      添加参数
                                    </Button>
                                  </Form.Item>
                                </>
                              )}
                            </Form.List>
                          );
                        }

                        if (bodyType === 'urlEncoded') {
                          return (
                            <Form.List name={['request', 'body', 'urlEncoded']}>
                              {(fields, { add, remove }) => (
                                <>
                                  {fields.map(({ key, name, ...restField }) => (
                                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'urlEncodedKey']}
                                        rules={[{ required: true, message: '请输入键' }]}
                                      >
                                        <Input placeholder="键" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'urlEncodedValue']}
                                        rules={[{ required: true, message: '请输入值' }]}
                                      >
                                        <Input placeholder="值" />
                                      </Form.Item>
                                      <MinusCircleOutlined onClick={() => remove(name)} />
                                    </Space>
                                  ))}
                                  <Form.Item>
                                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                      添加参数
                                    </Button>
                                  </Form.Item>
                                </>
                              )}
                            </Form.List>
                          );
                        }

                        if (['raw', 'xml', 'json'].includes(bodyType)) {
                          return (
                            <Form.Item name={['request', 'body', `${bodyType}Body`]} label={`${bodyType.toUpperCase()} 请求体`}>
                              <Input.TextArea rows={4} placeholder={`请输入 ${bodyType.toUpperCase()} 格式的请求体`} />
                            </Form.Item>
                          );
                        }

                        return null;
                      }}
                    </Form.Item>
                  </TabPane>
                </Tabs>
              </Form.Item>

              <Form.Item label="请求超时" required={true}>
                <Input.Group compact>
                  <div style={{ display: 'inline-block', width: '80%' }}>
                    <Form.Item noStyle rules={[{ required: true, message: '请输入请求超时时间' }]} name="apiQueryTimeout">
                      <InputNumber style={{ width: '100%' }} />
                    </Form.Item>
                  </div>
                  <div style={{ display: 'inline-block', width: '20%' }}>
                    <Form.Item noStyle>
                      <Input defaultValue="秒" disabled />
                    </Form.Item>
                  </div>
                </Input.Group>
              </Form.Item>

              <Form.Item className="form-actions center">
                <Button type="primary" onClick={handleApiNext}>
                  下一步
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane key="2">
            <div className="extract-placeholder">请先完成API连接设置</div>
          </TabPane>
        </Tabs>
      </Modal>
    </>
  );
};

export default DataSourceModal;
