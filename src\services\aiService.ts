export async function sendToAI(
  params: any,
  onMessageChunk: (chunk: { chatId: string; data: string; time: string; type: string }) => void,
): Promise<void> {
  try {
    const response = await fetch('/admin/Ai/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params),
    });

    if (!response.body) {
      throw new Error('响应异常，可能是 API Key 或服务器错误');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';
    let chatId = '';
    let time = '';
    let currentType = 'result';

    while (true) {
      let result;
      try {
        result = await reader.read();
      } catch (readErr) {
        console.error('流读取失败:', readErr);
        break;
      }
      const { done, value } = result;
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      let lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const lineRaw of lines) {
        const line = lineRaw.trim();
        if (line.startsWith('event:')) {
          currentType = line.replace('event:', '').trim();
        } else if (line.startsWith('data:')) {
          const jsonStr = line.replace('data:', '').trim();
          if (jsonStr === '[DONE]') return;
          try {
            const parsed = JSON.parse(jsonStr);
            chatId = parsed.chatId || chatId;
            time = parsed.time || new Date().toISOString();

            // 统一处理所有事件类型
            const eventData = {
              chatId,
              time,
              type: currentType,
              data: parsed.data || '',
            };

            onMessageChunk(eventData);
          } catch (jsonErr) {
            console.warn('JSON 解析失败:', jsonStr, jsonErr);
          }
        }
      }
    }
  } catch (err) {
    console.error('AI服务错误:', err);
    throw err;
  }
}
