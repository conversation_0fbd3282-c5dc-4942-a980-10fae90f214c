.assistant-table-section {
  .system-filter {
    background: #fff;
    border-radius: 8px;
    padding: 16px 24px;
    margin-bottom: 16px;
  }

  .system-filter-row {
    width: 100%;
  }

  .system-filter-item {
    display: inline-flex;
    align-items: center;
    .system-filter-item-label {
      min-width: 80px;
    }
  }

  .system-filter-item .ant-select {
    width: 220px;
  }

  .system-filter-item .ant-input {
    width: 220px;
  }

  .system-filter-btns {
    display: inline-flex;
    align-items: center;
  }

  .system-filter-reset {
    margin-right: 8px;
  }

  .system-operations {
    background: #fff;
    margin-bottom: 12px;
  }

  .system-table-wrapper {
    height: calc(100vh - 230px);
    background: #fff;
    border-radius: 8px;
    padding: 16px;
  }

  .system-action-edit {
    color: #0dc0e3;
  }

  .system-action-delete {
    color: #fc474c;
  }

  .system-pagination-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
  }
}
