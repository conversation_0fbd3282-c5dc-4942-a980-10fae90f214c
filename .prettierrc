{"printWidth": 140, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "consistent", "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "htmlWhitespaceSensitivity": "css", "overrides": [{"files": ["*.tsx"], "options": {"semi": true, "jsxSingleQuote": false}}, {"files": ["*.js", "*.jsx"], "options": {"singleQuote": false, "jsxSingleQuote": false}}, {"files": ["*.json", "*.md"], "options": {"singleQuote": false, "trailingComma": "all"}}, {"files": ["*.{css,scss}"], "options": {"singleQuote": false}}]}