.upload-form {
  .ant-form-item-label > label {
    font-size: 14px;
    color: #222;
    font-weight: 600;
  }
  .ant-input,
  .ant-input-textarea {
    border-radius: 4px;
    font-size: 14px;
    padding: 8px;
  }
}

.update-mode {
  margin-bottom: 20px;

  .ant-radio-wrapper {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    line-height: 1.6;
    margin-right: 24px;

    .ant-radio {
      margin-top: 0;
    }
  }
  .ant-space-item {
    margin-right: 24px;
  }
}

.upload-container {
  margin-bottom: 16px;

  .upload-dragger {
    background: #fafbfc;
    border-radius: 8px;
    min-height: 160px;
    transition: border-color 0.2s;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: center;

    &:hover,
    &.ant-upload-drag-hover {
      border-color: #2868e7;
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      .upload-icon {
        width: 52px;
        height: 52px;
        margin-bottom: 10px;
      }
      .upload-text {
        color: #262628;
        font-size: 14px;
        margin-bottom: 0;
      }
      .upload-desc,
      .upload-format {
        font-size: 12px;
        color: #9ca3af;
        margin-bottom: 0;
      }
      .upload-format {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }
}
.ant-form-item-explain-error {
  color: #e94f4f;
}
