import React, { useEffect, useRef, useState } from 'react';

interface SequenceFrameProps {
  images: string[]; // 图片URL数组
  duration?: number; // 每帧持续时间(ms)
  play?: boolean; // 是否播放
  onEnd?: () => void; // 播放结束回调
}

const SequenceFrame: React.FC<SequenceFrameProps> = ({ images, duration = 50, play = false, onEnd }) => {
  const [currentFrame, setCurrentFrame] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const timerRef = useRef<NodeJS.Timeout>();

  const startPlay = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setCurrentFrame((prev) => {
        const next = prev + 1;
        if (next >= images.length) {
          clearInterval(timerRef.current);
          onEnd?.();
          return prev;
        }
        return next;
      });
    }, duration);
  };

  const stopPlay = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setCurrentFrame(0);
  };

  useEffect(() => {
    if (play) {
      startPlay();
    } else {
      stopPlay();
    }
  }, [play]);

  return (
    <div
      onMouseEnter={() => setIsPlaying(true)}
      onMouseLeave={() => {
        setIsPlaying(false);
        stopPlay();
      }}
    >
      <img src={images[currentFrame]} alt={`Frame ${currentFrame + 1}`} />
    </div>
  );
};

export default SequenceFrame;
