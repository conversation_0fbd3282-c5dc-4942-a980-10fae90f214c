// 侧边栏宽度相关常量
export const MAX_WIDTH = 800;
export const MIN_WIDTH = 300; // 添加最小宽度常量
export const INITIAL_WIDTH_PERCENT = 30; // 初始宽度为视窗宽度的30%

// 添加常量定义
export const DEFAULT_ANALYST_TYPE = 'guider';

// 数据类型映射
export const DATA_TYPE_MAP: Record<string, number> = {
  default: 0,
  excel: 1,
  mysql: 2,
};

// 计算初始宽度的函数
export const calculateInitialWidth = () => {
  const viewportWidth = window.innerWidth;
  const initialWidth = Math.min(viewportWidth * (INITIAL_WIDTH_PERCENT / 100), MAX_WIDTH);
  return initialWidth;
};

// 判断是否为引导者类型
export const isGuider = (analyst?: { type?: string }) => analyst?.type === DEFAULT_ANALYST_TYPE;

// 格式化时间戳
export const formatTimestamp = () => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
};

// 解析上传文件
export const parseUploadedFiles = (uploadedFiles: unknown): string[] => {
  if (!uploadedFiles || uploadedFiles === '[""]') return [];

  if (typeof uploadedFiles === 'string') {
    try {
      // 尝试 JSON.parse（适用于 '["a.csv","b.csv"]'）
      const parsed = JSON.parse(uploadedFiles);
      if (Array.isArray(parsed)) {
        return parsed.map((item) => item.trim());
      }
    } catch (err) {
      // 非 JSON 格式（如 '[a.csv,b.csv]'），手动处理
      return uploadedFiles
        .slice(1, -1) // 去除 [ 和 ]
        .split(',')
        .map((item) => item.trim());
    }
  }

  if (Array.isArray(uploadedFiles)) {
    return uploadedFiles.map((item) => String(item).trim());
  }

  return [];
};

// 复制到剪贴板
export const copyToClipboard = async (text: string) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return { success: true };
    } else {
      // 回退方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        if (success) {
          return { success: true };
        } else {
          return { success: false, error: 'execCommand failed' };
        }
      } catch (err) {
        console.error('复制失败:', err);
        document.body.removeChild(textArea);
        return { success: false, error: err };
      }
    }
  } catch (err) {
    console.error('复制失败:', err);
    return { success: false, error: err };
  }
};

// 过滤树形数据的函数
export const filterTreeData = (data: any[], searchText: string): any[] => {
  if (!searchText) return data;

  return data
    .map((node) => {
      const title = typeof node.title === 'string' ? node.title.toLowerCase() : '';
      const searchLower = searchText.toLowerCase();

      // 检查当前节点是否匹配
      const isMatch = title.includes(searchLower);

      // 递归处理子节点
      const children = node.children ? filterTreeData(node.children, searchText) : [];

      // 如果当前节点匹配或者有匹配的子节点，则保留该节点
      if (isMatch || children.length > 0) {
        return {
          ...node,
          children: children.length > 0 ? children : undefined,
        };
      }

      return null;
    })
    .filter((node): node is NonNullable<typeof node> => node !== null);
};
