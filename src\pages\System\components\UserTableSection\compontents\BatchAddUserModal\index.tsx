import React, { useState } from 'react';
import { Modal, Upload, Button, Progress, Alert, Space, message } from 'antd';
import { InboxOutlined, DownloadOutlined } from '@ant-design/icons';
import './index.less';
import { downloadUserTemplate, importUsers } from '@/services/DataLoom/userController';

interface BatchAddUserModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk?: () => void;
}

const BatchAddUserModal: React.FC<BatchAddUserModalProps> = ({ visible, onOk, onCancel }) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [fileList, setFileList] = useState<any[]>([]);

  // 处理文件上传
  const handleUpload = async (file: File) => {
    // 文件类型验证
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('只能上传 Excel 文件！');
      return false;
    }

    // 文件大小验证 (10MB)
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB！');
      return false;
    }

    setUploading(true);
    setProgress(0);
    setFileList([file]);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // 调用导入接口
      const response = await importUsers(file);

      clearInterval(progressInterval);
      setProgress(100);

      if (response.code === 0) {
        message.success('用户批量导入成功！');
        setFileList([]);
        setProgress(0);
        setUploading(false);
        onOk?.();
      } else {
        message.error(response.message || '导入失败，请检查文件格式和内容');
        setProgress(0);
        setUploading(false);
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败，请稍后重试');
      setProgress(0);
      setUploading(false);
    }

    return false; // 阻止自动上传
  };

  // 处理模板下载
  const handleDownloadTemplate = async () => {
    try {
      const response = await downloadUserTemplate();

      // 创建下载链接
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '用户批量导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('模板下载成功！');
    } catch (error) {
      console.error('下载失败:', error);
      message.error('模板下载失败，请稍后重试');
    }
  };

  // 处理文件移除
  const handleRemove = () => {
    setFileList([]);
    setProgress(0);
    setUploading(false);
  };

  return (
    <Modal
      title="批量新建"
      open={visible}
      onCancel={onCancel}
      onOk={onOk}
      footer={null}
      width={750}
      destroyOnHidden
      className="batch-add-user-modal common-modal"
    >
      <Alert
        type="warning"
        className="alert-warning"
        message={
          <div className="alert-content">
            <img src="/assets/image_1751960303678_x89b74.svg" alt="warning" className="alert-icon" />
            <span>
              为防止导入失败，请仔细阅读模板内的注意事项。导入仅支持xls、xlsx文件，单次上传不允许超过1000条，超出部分无法导入，大小不超过10M
            </span>
          </div>
        }
      />
      {uploading && <Progress percent={progress} status={progress < 100 ? 'active' : 'success'} className="progress-bar" />}
      <Upload.Dragger
        name="file"
        accept=".xls,.xlsx"
        beforeUpload={handleUpload}
        fileList={fileList}
        onRemove={handleRemove}
        disabled={uploading}
        className="upload-container"
      >
        <p className="ant-upload-drag-icon">
          <img src="/assets/2thshumk.svg" alt="upload" className="upload-icon" />
        </p>
        <p className="ant-upload-text">
          点击或拖拽上传文件, 或者 <span className="select-file">选择文件</span>
        </p>
        <p className="ant-upload-hint">文件格式支持xls、xlsx,文件大小10MB以内</p>
      </Upload.Dragger>
      <div className="footer-container">
        <div className="template-download" onClick={handleDownloadTemplate}>
          <img src="/assets/image_1751960305069_ew7xlz.svg" alt="download" className="download-icon" />
          <span className="download-text">下载导入模板</span>
        </div>
        <Button onClick={onCancel}>取消</Button>
      </div>
    </Modal>
  );
};

export default BatchAddUserModal;
