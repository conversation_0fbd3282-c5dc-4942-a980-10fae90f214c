// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加仪表盘 POST /admin/dashboard/add */
export async function addDashboard(body: API.AddDashboardRequestDTO, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/dashboard/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 展示用户所有仪表盘 GET /admin/dashboard/listAllDashboard */
export async function listAllDashboard(options?: { [key: string]: any }) {
  return request<API.BaseResponseListDashboard>('/admin/dashboard/listAllDashboard', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据仪表盘id查询所有图表 GET /admin/dashboard/listAllChart */
export async function listChartByDashboardId(params: any, options?: { [key: string]: any }) {
  return request<any>('/admin/dashboard/listAllChart', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
/** 删除仪表盘 POST /admin/dashboard/deleteDashboard */
export async function deleteDashboard(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteDashboardParams,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseBoolean>('/admin/dashboard/deleteDashboard', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加图表 POST /admin/dashboard/addChart */
export async function addDashboardChart(body: any, options?: { [key: string]: any }) {
  return request<API.BaseResponseLong>('/admin/dashboard/addChart', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据图表id获取数据 GET /admin/dashboard/getChartDataById */
export async function getChartDataById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getChartDataByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseGetChartDataVO>('/admin/dashboard/getChartDataById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取数据源所有表信息 GET /admin/coreDatasource/getTables */
export async function getTables(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: any,
  options?: { [key: string]: any },
) {
  return request<any>('/admin/coreDatasource/getTables', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 保存AI生成的仪表 POST /admin/dashboard/addAiChart */
export async function addAiChart(body: any, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/dashboard/addAiChart', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id查询仪表盘 GET /admin/dashboard/getDashboardById */
export async function getDashboardById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: any,
  options?: { [key: string]: any },
) {
  return request<any>('/admin/dashboard/getDashboardById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改图表 POST /admin/dashboard/editChart */
export async function editChart(body: any, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/dashboard/editChart', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除仪表 POST /admin/dashboard/deleteChart */
export async function deleteChart(params: any, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/dashboard/deleteChart', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
/** ai问题推荐; /admin/Ai/aiSuggest */
export async function aiSuggest(body: any, options?: { [key: string]: any }) {
  return request<any>('/admin/Ai/aiSuggest', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}
/** 根据数据源id获取数据源信息 GET /admin/coreDatasource/get */
export async function getDatasourceById(params: any, options?: { [key: string]: any }) {
  return request<any>('/admin/coreDatasource/get', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
/** 修改聊天记录中图表样式; /admin/Ai/update/chartStyle */
export async function updateChartStyle(body: any, options?: { [key: string]: any }) {
  return request<any>('/admin/Ai/update/chartStyle', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}
/**  chart数据下载; /admin/Ai/download */
export async function downloadChartData(body: any, options?: { [key: string]: any }) {
  return request<any>('/admin/Ai/download', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 仪表数据预览 POST /admin/dashboard/data/preview */
export async function dataPreview(body: API.DataPreviewRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseDataPreview>('/admin/dashboard/data/preview', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
