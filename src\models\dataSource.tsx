// src/models/dataSource.ts

import { useState } from 'react';

export interface DataSourceItem {
  id: string;
  name: string;
  type: string;
  table?: string;
  datasourceId: string;
}

export default () => {
  const [selectedSources, setSelectedSources] = useState<DataSourceItem[]>([]);

  // 添加数据源（避免重复）
  const addSource = (source: DataSourceItem) => {
    setSelectedSources((prev) => {
      if (prev.find((item) => item.id === source.id)) return prev;
      return [...prev, source];
    });
  };

  // 删除数据源
  const removeSource = (id: string) => {
    setSelectedSources((prev) => prev.filter((item) => item.id !== id));
  };

  // 清空所有
  const clearSources = () => {
    setSelectedSources([]);
  };

  return {
    selectedSources,
    addSource,
    removeSource,
    clearSources,
  };
};
