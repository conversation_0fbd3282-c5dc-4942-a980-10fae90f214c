import { useState } from 'react';

export interface ChatMessage {
  id?: string;
  role?: 'user' | 'assistant';
  content?: string;
  timestamp?: string;
  echart_option?: any;
  uploaded_files?: string[];
  chartStyle?: string;
}

export default () => {
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);

  // 添加新的聊天消息
  const addMessage = (message: ChatMessage) => {
    setChatHistory((prev) => [...prev, message]);
  };

  // 删除指定消息
  const removeMessage = (id: string) => {
    setChatHistory((prev) => prev.filter((msg) => msg.id !== id));
  };

  // 更新指定消息
  const updateMessage = (id: string, updates: Partial<Omit<ChatMessage, 'id' | 'timestamp'>>) => {
    setChatHistory((prev) =>
      prev.map((msg) =>
        msg.id === id
          ? {
              ...msg,
              ...updates,
            }
          : msg,
      ),
    );
  };

  // 清空聊天记录
  const clearHistory = () => {
    setChatHistory([]);
  };

  // 获取最近的聊天记录
  const getRecentMessages = (limit: number = 10) => {
    return chatHistory.slice(-limit);
  };

  return {
    chatHistory,
    addMessage,
    removeMessage,
    updateMessage,
    clearHistory,
    getRecentMessages,
  };
};
