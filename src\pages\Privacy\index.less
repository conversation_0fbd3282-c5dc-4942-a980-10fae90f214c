.privacy-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;

  .privacy-card {
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .privacy-header {
      text-align: center;
      padding: 40px 0 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: -24px -24px 32px -24px;

      .privacy-title {
        color: white !important;
        margin-bottom: 8px;
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .privacy-date {
        color: rgba(255, 255, 255, 0.9) !important;
        font-size: 1rem;
      }
    }

    .privacy-content {
      display: flex;
      gap: 32px;
      position: relative;

      .privacy-anchor {
        position: sticky;
        top: 40px;
        width: 200px;
        height: fit-content;
        background: #fafafa;
        border-radius: 8px;
        padding: 16px;
        border: 1px solid #e8e8e8;
        flex-shrink: 0;

        :global(.ant-anchor-link-title) {
          font-size: 14px;
          color: #666;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }

        :global(.ant-anchor-link-active > .ant-anchor-link-title) {
          color: #1890ff;
          font-weight: 600;
        }

        :global(.ant-anchor-ink::before) {
          background-color: #e8e8e8;
        }

        :global(.ant-anchor-ink-ball) {
          background-color: #1890ff;
        }
      }

      .privacy-main {
        flex: 1;
        min-width: 0;

        section {
          margin-bottom: 32px;
          scroll-margin-top: 100px;

          h2 {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 8px;
            margin-bottom: 24px;
            font-weight: 600;
          }

          h3 {
            color: #333;
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
          }

          h4 {
            color: #555;
            margin-top: 16px;
            margin-bottom: 12px;
            font-weight: 500;
          }

          p {
            line-height: 1.8;
            color: #666;
            margin-bottom: 16px;
            text-align: justify;
          }

          ul {
            margin-left: 20px;
            margin-bottom: 16px;

            li {
              line-height: 1.8;
              color: #666;
              margin-bottom: 8px;
              position: relative;

              &::marker {
                color: #1890ff;
              }
            }
          }
        }

        :global(.ant-divider) {
          margin: 40px 0;
          border-color: #e8e8e8;
        }
      }
    }
  }

  .back-top-button {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(102, 126, 234, 0.6);
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .privacy-container {
    .privacy-card {
      .privacy-content {
        flex-direction: column;

        .privacy-anchor {
          position: static;
          width: 100%;
          order: -1;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .privacy-container {
    padding: 16px;

    .privacy-card {
      .privacy-header {
        padding: 24px 16px 16px;
        margin: -24px -24px 24px -24px;

        .privacy-title {
          font-size: 2rem;
        }

        .privacy-date {
          font-size: 0.9rem;
        }
      }

      .privacy-content {
        gap: 24px;

        .privacy-anchor {
          padding: 12px;
        }

        .privacy-main {
          section {
            margin-bottom: 24px;

            h2 {
              font-size: 1.5rem;
              margin-bottom: 16px;
            }

            h3 {
              font-size: 1.2rem;
              margin-top: 16px;
              margin-bottom: 12px;
            }

            h4 {
              font-size: 1.1rem;
              margin-top: 12px;
              margin-bottom: 8px;
            }

            p {
              font-size: 14px;
              line-height: 1.6;
            }

            ul {
              margin-left: 16px;

              li {
                font-size: 14px;
                line-height: 1.6;
              }
            }
          }

          :global(.ant-divider) {
            margin: 24px 0;
          }
        }
      }
    }
  }
}

// 打印样式
@media print {
  .privacy-container {
    background: white;
    padding: 0;

    .privacy-card {
      box-shadow: none;
      border: none;

      .privacy-header {
        background: white;
        color: black;
        margin: 0;

        .privacy-title {
          color: black !important;
          text-shadow: none;
        }

        .privacy-date {
          color: #666 !important;
        }
      }

      .privacy-content {
        .privacy-anchor {
          display: none;
        }

        .privacy-main {
          section {
            h2 {
              color: black;
              border-bottom-color: #ccc;
            }

            h3,
            h4 {
              color: black;
            }

            p,
            li {
              color: black;
            }
          }
        }
      }
    }

    .back-top-button {
      display: none;
    }
  }
}
