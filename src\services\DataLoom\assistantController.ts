// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加智能助手 POST /admin/aiRole/add */
export async function addAiRole(formData: FormData, options?: { [key: string]: any }) {
  return request<API.BaseResponseLong>('/admin/aiRole/add', {
    method: 'POST',
    // 不设置Content-Type，浏览器会自动设置为multipart/form-data
    data: formData,
    ...(options || {}),
  });
}

/** 删除智能助手 POST /admin/aiRole/delete */
export async function deleteAiRole(body: API.DeleteRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/aiRole/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改智能助手 POST /admin/aiRole/update */
export async function updateAiRole(formData: FormData, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/aiRole/update', {
    method: 'POST',
    // 不设置Content-Type，浏览器会自动设置为multipart/form-data
    data: formData,
    ...(options || {}),
  });
}

/** 根据id查询智能助手 GET /admin/aiRole/get/vo */
export async function getAiRoleById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAiRoleByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseAiRole>('/admin/aiRole/get/vo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除知识库文件 GET /admin/aiRole/file/delete */
export async function deleteAiRoleFile(
  params: {
    fileName: string;
    id: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseBoolean>('/admin/aiRole/file/delete', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 智能助手分页列表 POST /admin/aiRole/list/page/vo */
export async function listAiRoleVoByPage(body: API.AiRoleQueryRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponsePageAiRole>('/admin/aiRole/list/page/vo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 智能助手列表（不带分页） GET /admin/aiRole/list */
export async function getAiRoleList(options?: { [key: string]: any }) {
  return request<API.BaseResponseListAiRole>('/admin/aiRole/list', {
    method: 'GET',
    ...(options || {}),
  });
}
