import React from 'react';
import './index.less';
import { useNavigate } from 'react-router-dom';
import { getIconBgColors, getImageByType, hasCustomImage } from '@/config/analyst'; // 引入颜色工具

interface Props {
  currentAnalyst: any;
  onAnalystChange: (type: string) => void;
  aiAssistantList: any;
}

const HotAssistantBar: React.FC<Props> = ({ currentAnalyst, onAnalystChange, aiAssistantList }) => {
  const navigate = useNavigate();

  return (
    <div className="hot-assistant-bar-container">
      <div className="hot-assistant-bar">
        {aiAssistantList.map((item: any, idx: number) => (
          <div
            key={item.type}
            className={`hot-assistant-btn ${currentAnalyst?.type === item?.type ? 'active' : ''}`}
            onClick={() => onAnalystChange(item)}
          >
            <div
              className="hot-assistant-icon"
              {...(!hasCustomImage(item.type) && { style: { backgroundColor: getIconBgColors(aiAssistantList.length)[idx] } })}
            >
              <img src={getImageByType(item.type || 'default')} alt={item.assistantName} />
            </div>
            <span title={item.assistantName}>{item.assistantName}</span>
          </div>
        ))}
        <div className="task-queue-btn" onClick={() => navigate('/task_queue')} style={{ cursor: 'pointer' }}>
          <img src="/assets/image_1752040821791_v609e9.svg" alt="任务队列" />
          <span>任务队列</span>
        </div>
      </div>
    </div>
  );
};

export default HotAssistantBar;
