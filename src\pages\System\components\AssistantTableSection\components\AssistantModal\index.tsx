import React, { useState } from 'react';
import { Modal, Form, Input, Select, Upload, Button, Checkbox, message, Progress } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import './index.less';
import _ from 'lodash';
import { addAiRole, updateAiRole, deleteAiRoleFile } from '@/services/DataLoom/assistantController';

const { Option } = Select;

interface AssistantModalProps {
  visible: boolean;
  mode: 'add' | 'edit';
  initialValues?: API.AiRole | null;
  onCancel: () => void;
  onOk: () => void;
}

const AssistantModal: React.FC<AssistantModalProps> = ({ visible, mode, initialValues, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [confirmLoading, setConfirmLoading] = useState(false);

  React.useEffect(() => {
    if (visible) {
      if (mode === 'edit' && initialValues) {
        // 处理历史文件，确保每个文件有唯一 uid，docFiles 为 JSON 字符串时先转为数组
        let docFilesArr: any[] = [];
        try {
          const raw = (initialValues as any)?.docFiles;
          if (typeof raw === 'string') {
            docFilesArr = JSON.parse(raw);
          } else if (Array.isArray(raw)) {
            docFilesArr = raw;
          }
        } catch (e) {
          docFilesArr = [];
        }
        const historyFiles = docFilesArr.map((f: any, idx: number) => {
          if (typeof f === 'string') {
            return {
              uid: `history-${idx}`,
              name: f.split('/').pop() || `历史文件${idx + 1}`,
              status: 'done',
              url: f,
            };
          }
          return {
            ...f,
            uid: f.uid || `history-${idx}`,
            name: f.name || f.fileName || `历史文件${idx + 1}`,
            status: f.status || 'done',
            url: f.url,
          };
        });
        form.setFieldsValue({
          assistantName: initialValues.assistantName,
          assistantType: initialValues.type,
          description: initialValues.functionDes,
          requirement: initialValues.requirement,
          template: initialValues.template,
          docFiles: historyFiles,
        });
      } else if (mode === 'add') {
        form.resetFields();
      }
    }
  }, [visible, mode, initialValues, form]);

  const handleOk = async () => {
    setConfirmLoading(true);
    try {
      const values = await form.validateFields();
      // 构造 request 对象
      const requestObj: {
        assistantName: any;
        type: any;
        functionDes: any;
        requirement: any;
        template: any;
        id?: any;
      } = {
        assistantName: values.assistantName,
        type: values.assistantType,
        functionDes: values.description,
        requirement: values.requirement,
        template: values.template,
      };
      const formData = new FormData();
      // 多文件上传
      if (values.docFiles && values.docFiles.length > 0) {
        values.docFiles.forEach((file: any) => {
          formData.append('files', file.originFileObj || file);
        });
      }
      // 其他参数打包成字符串
      formData.append('request', JSON.stringify(requestObj));
      let res;
      if (mode === 'add') {
        res = await addAiRole(formData);
      } else {
        // 编辑时需要带上 id
        if (initialValues && initialValues.id) {
          requestObj.id = initialValues.id;
        }
        formData.set('request', JSON.stringify(requestObj)); // 更新 request 字段
        res = await updateAiRole(formData);
      }
      if (res.code === 0) {
        message.success(mode === 'add' ? '新建助手成功' : '编辑助手成功');
        form.resetFields();
        onOk();
      } else {
        message.error(res.message || (mode === 'add' ? '新建助手失败' : '编辑助手失败'));
      }
      setConfirmLoading(false);
    } catch (e) {
      // 校验失败或请求异常
      setConfirmLoading(false);
    }
  };

  // 处理文件上传逻辑，并与表单联动
  const handleUpload = (file: File) => {
    const isLt15M = file.size / 1024 / 1024 < 15;
    if (!isLt15M) {
      message.error('文件大小不能超过15MB！');
      return false;
    }
    setUploading(true);
    setProgress(0);
    // 给 file 加唯一 uid
    (file as any).uid = (file as any).uid || `upload-${Date.now()}-${file.name}`;
    // 模拟上传进度
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setUploading(false);
          message.success(`${file.name} 上传成功！`);
          // 设置表单字段，支持多文件，且去重
          const currentFiles = form.getFieldValue('docFiles') || [];
          // antd Upload 组件会给 file 自动加 uid
          const exists = currentFiles.some((f: any) => f.uid === (file as any).uid || (f.name === file.name && f.size === file.size));
          if (!exists) {
            form.setFieldsValue({ docFiles: [...currentFiles, file] });
          }
          return 100;
        }
        return prev + 20;
      });
    }, 200);
    // 阻止自动上传
    return false;
  };

  // 处理 Dragger 的 value 到表单的转换
  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList ? e.fileList.map((item: any) => item.originFileObj || item) : [];
  };

  const handleRemove = async (file: any) => {
    // 只处理历史文件（有 url 字段，且不是刚上传的）
    if (file.url && initialValues && initialValues.id) {
      try {
        const res = await deleteAiRoleFile({
          id: initialValues.id,
          fileName: file.url, // 按接口定义参数名修正
        });
        if (res.code === 0) {
          message.success('文件删除成功');
          // 移除表单中的该文件
          const currentFiles = form.getFieldValue('docFiles') || [];
          form.setFieldsValue({
            docFiles: currentFiles.filter((f: any) => f.uid !== file.uid),
          });
          return true;
        } else {
          message.error(res.message || '文件删除失败');
          return false;
        }
      } catch (e) {
        message.error('文件删除异常');
        return false;
      }
    }
    // 新上传未保存的文件，直接允许移除
    return true;
  };

  return (
    <Modal
      title={mode === 'add' ? '新增助手' : '编辑助手'}
      className="add-assistant-modal common-modal"
      open={visible}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={handleOk}
      okText="确认"
      cancelText="取消"
      width={750}
      destroyOnHidden={true}
      confirmLoading={confirmLoading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          assistantName: '',
          assistantType: undefined,
          description: '',
          requirement: '',
          template: [],
        }}
      >
        <Form.Item label="助手名称" name="assistantName" rules={[{ required: true, message: '请输入助手名称' }]}>
          <Input placeholder="请输入内容" />
        </Form.Item>
        <Form.Item label="助手类型" name="assistantType" rules={[{ required: true, message: '请选择助手类型' }]}>
          <Input placeholder="请输入内容" />
        </Form.Item>
        <Form.Item label="功能描述" name="description" rules={[{ required: true, message: '请输入功能描述' }]}>
          <Input.TextArea placeholder="请输入功能描述" rows={3} />
        </Form.Item>
        <Form.Item
          label="知识库上传"
          name="docFiles"
          valuePropName="fileList"
          getValueFromEvent={normFile}
          rules={[{ required: false, message: '请上传知识库文件' }]}
        >
          <>
            {uploading && <Progress percent={progress} status={progress < 100 ? 'active' : 'success'} className="progress-bar" />}
            <Upload.Dragger
              name="file"
              accept=".pdf,.docx"
              beforeUpload={handleUpload}
              showUploadList={true}
              multiple={true}
              disabled={uploading}
              className="upload-container"
              maxCount={10}
              onRemove={handleRemove}
            >
              <p className="ant-upload-drag-icon">
                <img src="/assets/2thshumk.svg" alt="upload" className="upload-icon" />
              </p>
              <p className="ant-upload-text">
                点击或拖拽上传文件, 或者 <span className="select-file">选择文件</span>
              </p>
              <p className="ant-upload-hint">仅支持PDF、DOCX格式，文件大小15MB以内，最多10个文件</p>
            </Upload.Dragger>
          </>
        </Form.Item>
        <Form.Item label="需求说明" name="requirement">
          <Input.TextArea placeholder="请输入需求说明" rows={3} />
        </Form.Item>
        <Form.Item label="输入模板" name="template">
          <Input.TextArea placeholder="请输入模板" rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AssistantModal;
