// 智能分析弹窗内容样式
.analysis-content {
  width: 350px;
}

.analysis-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .analysis-icon {
    width: 36px;
    height: 36px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .analysis-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2868e7;
  }
}

.common-paragraph.paragraph-body {
  max-height: 300px;
  overflow-y: auto;
  h3 {
    width: max-content;
    padding: 2px 8px;
    font-weight: 600;
    border-radius: 4px;
    font-size: 14px;
  }
  h3:nth-of-type(1) {
    border: 1px solid #50b3f5;
    background: rgba(142, 207, 251, 0.6);
  }
  h3:nth-of-type(2) {
    border: 1px solid #e86541;
    background: rgba(239, 151, 151, 0.6);
  }
  h3:nth-of-type(3) {
    border: 1px solid #1d4fd7;
    background: rgba(29, 79, 215, 0.6);
  }
  h3:nth-of-type(4) {
    border: 1px solid #e86541;
    background: rgba(239, 151, 151, 0.6);
  }
  h3:nth-of-type(5) {
    border: 1px solid #e86541;
    background: rgba(239, 151, 151, 0.6);
  }
}
