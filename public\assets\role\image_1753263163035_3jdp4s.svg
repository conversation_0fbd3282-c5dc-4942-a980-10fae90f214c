<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5701_34354)">
<g filter="url(#filter0_f_5701_34354)">
<rect x="13.2109" y="37.2637" width="26.9192" height="4.3418" fill="#55A4FF" fill-opacity="0.8"/>
</g>
<ellipse cx="26.6732" cy="40.7395" rx="3.90762" ry="0.86836" fill="url(#paint0_radial_5701_34354)" fill-opacity="0.7"/>
<rect x="8" y="6" width="26.0508" height="26.0508" rx="4" fill="url(#paint1_linear_5701_34354)"/>
<foreignObject x="8.34375" y="8.08008" width="36.6562" height="36.6562"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_5701_34354_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_5701_34354)" data-figma-bg-blur-radius="4">
<rect x="12.3438" y="12.0801" width="28.6559" height="28.6559" rx="4" fill="#99E2FF" fill-opacity="0.4"/>
<rect x="12.5938" y="12.3301" width="28.1559" height="28.1559" rx="3.75" stroke="url(#paint2_linear_5701_34354)" stroke-opacity="0.7" stroke-width="0.5"/>
</g>
<g filter="url(#filter2_di_5701_34354)">
<path d="M35.4716 33.9967C35.7692 33.9967 36.0104 34.2396 36.0104 34.5392C36.0104 34.8388 35.7692 35.0817 35.4716 35.0817H19.3083C19.0108 35.0817 18.7695 34.8388 18.7695 34.5392C18.7695 34.2396 19.0108 33.9967 19.3083 33.9967H35.4716ZM20.6553 21.5181C21.3992 21.5181 22.0022 22.1254 22.0022 22.8745V31.5552C22.0022 32.3043 21.3992 32.9116 20.6553 32.9116C19.9114 32.9116 19.3083 32.3043 19.3083 31.5552V22.8745C19.3083 22.1254 19.9114 21.5181 20.6553 21.5181ZM24.9655 17.1777C25.7094 17.1777 26.3124 17.785 26.3124 18.5341V31.5552C26.3124 32.3043 25.7094 32.9116 24.9655 32.9116C24.2216 32.9116 23.6185 32.3043 23.6185 31.5552V18.5341C23.6185 17.785 24.2216 17.1777 24.9655 17.1777ZM29.2757 24.7734C30.0196 24.7734 30.6226 25.3806 30.6226 26.1297V31.5552C30.6226 32.3043 30.0196 32.9116 29.2757 32.9116C28.5318 32.9116 27.9288 32.3043 27.9288 31.5552V26.1297C27.9288 25.3806 28.5318 24.7734 29.2757 24.7734ZM33.5859 21.5181C34.3298 21.5181 34.9329 22.1254 34.9329 22.8745V31.5552C34.9329 32.3043 34.3298 32.9116 33.5859 32.9116C32.842 32.9116 32.239 32.3043 32.239 31.5552V22.8745C32.239 22.1254 32.842 21.5181 33.5859 21.5181Z" fill="url(#paint3_linear_5701_34354)"/>
</g>
</g>
<defs>
<filter id="filter0_f_5701_34354" x="2.81094" y="26.8637" width="47.718" height="25.1418" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.2" result="effect1_foregroundBlur_5701_34354"/>
</filter>
<filter id="filter1_i_5701_34354" x="8.34375" y="8.08008" width="36.6562" height="36.6562" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0581084 0 0 0 0 0.681159 0 0 0 0 0.805769 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5701_34354"/>
</filter>
<clipPath id="bgblur_1_5701_34354_clip_path" transform="translate(-8.34375 -8.08008)"><rect x="12.3438" y="12.0801" width="28.6559" height="28.6559" rx="4"/>
</clipPath><filter id="filter2_di_5701_34354" x="17.7695" y="17.1777" width="19.2422" height="19.9043" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0748799 0 0 0 0 0.582458 0 0 0 0 0.855769 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5701_34354"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5701_34354" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670656 0 0 0 0 0.923362 0 0 0 0 0.986538 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5701_34354"/>
</filter>
<radialGradient id="paint0_radial_5701_34354" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(26.6732 40.7395) scale(3.90762 0.86836)">
<stop offset="0.508354" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_5701_34354" x1="21.0254" y1="6" x2="21.0254" y2="32.0508" gradientUnits="userSpaceOnUse">
<stop stop-color="#0777FF"/>
<stop offset="1" stop-color="#1DA5FF"/>
</linearGradient>
<linearGradient id="paint2_linear_5701_34354" x1="13.6173" y1="15.9009" x2="38.4525" y2="38.5072" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAF6FF"/>
<stop offset="1" stop-color="#6BECFF"/>
</linearGradient>
<linearGradient id="paint3_linear_5701_34354" x1="27.39" y1="17.1777" x2="27.39" y2="35.0817" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CFECFF"/>
</linearGradient>
<clipPath id="clip0_5701_34354">
<rect width="48" height="48" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
