import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Row, Col } from 'antd';
import { resetUserPassword } from '@/services/DataLoom/userController';
import { message } from 'antd';

interface ResetPasswordModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  accountName?: string;
  id?: number;
}

const getPasswordStrength = (pwd: string) => {
  if (!pwd) return 0;
  let score = 0;
  if (pwd.length >= 6) score += 1;
  if (/[A-Z]/.test(pwd) && /[a-z]/.test(pwd)) score += 1;
  if (/[0-9]/.test(pwd) && /[^A-Za-z0-9]/.test(pwd)) score += 1;
  return score;
};

const strengthMap = ['弱', '中', '强'];

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({ visible, onCancel, onOk, accountName, id }) => {
  const [form] = Form.useForm();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && accountName) {
      form.setFieldsValue({ accountName });
    }
  }, [visible, accountName, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const userId = id ? id.toString() : accountName;
      if (!userId) {
        message.error('用户ID缺失，无法重置密码');
        return;
      }
      setLoading(true);
      const res = await resetUserPassword({ id: userId, newPassword: values.password });
      if (res.code === 0 && res.data) {
        message.success('密码重置成功');
        onOk(values);
        onCancel();
        form.resetFields();
        setPassword('');
      } else {
        message.error(res.message || '密码重置失败');
      }
    } catch (error) {
      // 校验失败或接口异常
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setPassword('');
    onCancel();
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
  };

  const strength = getPasswordStrength(password);

  return (
    <Modal
      title="重置密码"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={700}
      destroyOnHidden
      className="reset-password-modal common-modal"
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical">
        <Form.Item label="账号名称" name="accountName" rules={[{ required: true, message: '请输入账号名称' }]}>
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="重置密码"
          name="password"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 6, message: '密码长度不能少于6个字符' },
            { max: 20, message: '密码长度不能超过20个字符' },
          ]}
        >
          <Input.Password
            placeholder="请输入新密码"
            visibilityToggle={{ visible: passwordVisible, onVisibleChange: setPasswordVisible }}
            onChange={handlePasswordChange}
          />
        </Form.Item>
        <Row align="middle" style={{ marginTop: -10, marginBottom: 16 }}>
          <Col flex="auto">
            <div style={{ display: 'flex', gap: '4px', width: '100%' }}>
              <div
                style={{
                  flex: 1,
                  height: '8px',
                  backgroundColor: strength >= 1 ? '#ff4d4f' : '#f5f5f5',
                  borderRadius: '4px',
                }}
              />
              <div
                style={{
                  flex: 1,
                  height: '8px',
                  backgroundColor: strength >= 2 ? '#faad14' : '#f5f5f5',
                  borderRadius: '4px',
                }}
              />
              <div
                style={{
                  flex: 1,
                  height: '8px',
                  backgroundColor: strength >= 3 ? '#52c41a' : '#f5f5f5',
                  borderRadius: '4px',
                }}
              />
            </div>
          </Col>
          <Col
            style={{
              textAlign: 'right',
              marginLeft: 6,
              color: strength === 1 ? '#ff4d4f' : strength === 2 ? '#faad14' : strength === 3 ? '#52c41a' : '#999',
            }}
          >
            {password ? strengthMap[strength - 1] || '弱' : ''}
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default ResetPasswordModal;
