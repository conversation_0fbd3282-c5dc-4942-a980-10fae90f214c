.data-source-container {
  position: relative;
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  padding: 12px 12px 16px 12px;
  flex-shrink: 0;
  background: #fafafa;

  .react-resizable-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 100%;
    cursor: col-resize;
    background: transparent;
    transition: background-color 0.2s;
    z-index: 100;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .start-button {
    border-radius: 4px;
    padding: 12px 0;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 16px;
    width: 100%;
    height: 48px;
    font-weight: 600;
    flex-shrink: 0;
  }

  .select-source-button {
    height: 48px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 16px;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    transition: background-color 0.2s;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover {
      background-color: #f8f9fa;
    }
  }

  .selection-container {
    position: absolute;
    width: calc(100% - 34px);
    height: calc(100% - 250px);
    top: 75px;
    left: 17px;
    right: 17px;
    bottom: 0;
    background: #fff;
    z-index: 10;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .selection-header {
      border: 1px dashed #ccc;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #4285f4;
      font-size: 14px;

      .selection-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .confirm-button {
        padding: 4px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;

        &:hover {
          background-color: #40a9ff;
        }

        &:disabled {
          background-color: #d9d9d9;
          cursor: not-allowed;
        }
      }
    }

    .selection-content {
      position: relative;
      border-radius: 4px;
      background: #fff;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      padding-bottom: 60px;
      overflow: hidden;
      flex: 1;

      .search-container {
        padding: 8px 16px;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      // 添加 Tree 组件选中状态的样式
      .ant-tree {
        .ant-tree-treenode {
          margin-bottom: 6px;
        }
        .ant-tree-node-content-wrapper {
          &:hover {
            background-color: #f5f5f5;
          }
        }

        .ant-tree-node-selected {
          background-color: #e6f7ff !important;
        }

        // 选中 checkbox 时的高亮样式
        .ant-tree-checkbox-checked + .ant-tree-node-content-wrapper {
          background-color: #e6f7ff;
          border-radius: 0;
        }
      }
    }
  }

  .chat-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .selected-source {
      border: 1px dashed #ccc;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #4285f4;
      cursor: pointer;

      .close-icon {
        width: 14px;
        height: 14px;
        margin-left: 8px;
      }
    }

    .chat-content {
      flex: 1;
      overflow-y: auto;
      min-height: 0;
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding-right: 16px;

      h4 {
        text-align: center;
        margin: 0 0 8px 0;
        font-size: 16px;
        animation: slideIn 0.3s ease-out;
      }

      .message {
        display: flex;
        flex-direction: column;
        max-width: 100%;
        margin-bottom: 10px;

        .message-timestamp {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 12px;
          color: #000;
          margin-bottom: 6px;
          .message-icon {
            img {
              width: 100%;
              height: 100%;
              cursor: pointer;
            }
          }
        }

        .message-content {
          padding: 8px;
          word-break: break-word;
          border-radius: 6px;
          position: relative;
          p {
            margin-top: 0;
            margin-bottom: 0;
          }

          .message-actions {
            position: absolute;
            right: 0;
            bottom: -32px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
            background: transparent;
            padding: 4px;
            border-radius: 4px;
            z-index: 1;

            .ant-btn {
              padding: 4px;
              height: 24px;
              width: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #666;
              border-radius: 4px;

              .anticon {
                font-size: 14px;
              }

              &:hover {
                color: #2868e7;
                background: #f0f0f0;
              }
            }
          }

          &:hover {
            .message-actions {
              opacity: 1;
            }
          }
        }

        &.user-message {
          align-self: flex-end;

          .message-timestamp {
            align-self: flex-end;
          }

          .message-content {
            background: #2868e7;
            box-shadow: 0px 0px 8px 0px rgba(7, 32, 77, 0.06);
            color: white;
            position: relative;

            .message-actions {
              position: absolute;
              right: 0;
              bottom: -32px;
              display: flex;
              gap: 4px;
              opacity: 0;
              transition: opacity 0.2s;
              background: transparent;
              padding: 4px;
              border-radius: 4px;
              z-index: 1;

              .ant-btn {
                padding: 4px;
                height: 24px;
                width: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                border-radius: 4px;

                .anticon {
                  font-size: 14px;
                }

                &:hover {
                  color: #2868e7;
                  background: #f0f0f0;
                }
              }
            }

            &:hover {
              .message-actions {
                opacity: 1;
              }
            }
          }
        }

        &.bot-message {
          align-self: flex-start;

          .message-content {
            background-color: #fff;
            color: #5c5c64;
            .message-content-wrapper {
              overflow-x: auto;
            }
          }
        }
        .tip-indicator {
        }
        .typing-indicator {
          display: flex;
          gap: 8px;
          padding: 12px 16px;

          span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            opacity: 0;
            animation: fadeInOut 1.4s infinite ease-in-out;

            &:nth-child(1) {
              background: #4285f4;
              animation-delay: 0s;
            }
            &:nth-child(2) {
              background: #7b61ff;
              animation-delay: 0.2s;
            }
            &:nth-child(3) {
              background: #9c27b0;
              animation-delay: 0.4s;
            }
          }
        }
      }

      .welcome-message {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        border-radius: 4px;
        background: #fff;
        animation: slideIn 0.3s ease-out;

        .welcome-icon {
          img {
            width: 100%;
            height: 100%;
            cursor: pointer;
          }
        }

        .welcome-text {
          flex: 1;
          font-size: 14px;
          line-height: 1.6;
          color: #262628;
        }
      }
    }

    .chat-footer {
      flex-shrink: 0;
      margin-top: 16px;
      .guess-you-want {
        margin-bottom: 0;
        .ant-collapse-header {
          padding: 0;
        }
        .ant-collapse-content-box {
          padding: 12px 0 0 0;
        }
        .guess-options {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          .question-btn {
            margin-right: 0;
            padding: 4px 15px;
            border: 1px solid #dceaff;
            background: #f5f9ff;
            border-radius: 8px;
            color: #262628;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;

            &:hover {
              background: #e8f1ff;
              border-color: #2868e7;
            }
            span {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 100%;
            }
          }
        }
      }

      .clear-button {
        background-color: white;
        color: rgba(0, 0, 0, 0.85);
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 4px 15px;
        font-size: 14px;
        cursor: pointer;
        margin: 12px 0;
        transition: all 0.2s;
        margin-left: auto;
        display: block;

        &:hover {
          background-color: #f8f9fa;
        }
      }

      .message-input-container {
        display: flex;
        gap: 8px;
        align-items: flex-end;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 8px;

        .message-input {
          flex: 1;
          border: none;
          outline: none;
          padding: 8px;
          resize: none;
          min-height: 24px;
          max-height: 120px;
          font-size: 14px;
          line-height: 1.5;

          &:disabled {
            background-color: #f5f5f5;
          }
        }

        .pause-button {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: none;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          flex-shrink: 0;
          padding: 0;
          transition: background-color 0.2s;
          img {
            width: 21px;
            height: 21px;
            fill: currentColor;
          }
          &:hover:not(:disabled) {
            background-color: #93bbff;
          }

          &:disabled {
            background-color: #fff;
            cursor: not-allowed;
          }
        }

        .send-button {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: none;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          flex-shrink: 0;
          padding: 0;
          transition: background-color 0.2s;

          &:hover:not(:disabled) {
            background-color: #93bbff;
          }

          &:disabled {
            background-color: #fff;
            cursor: not-allowed;
          }

          svg {
            width: 21px;
            height: 21px;
            fill: currentColor;
          }
        }
      }
    }
  }

  .file-name-text {
    display: inline-block;
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

body.resizing {
  cursor: col-resize !important;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

body.resizing .react-resizable-handle {
  background-color: rgba(0, 0, 0, 0.1);
}

@keyframes typing {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.selection-footer {
  position: absolute;
  right: 16px;
  bottom: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  background: #fff;
}

.selected-analyst {
  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 100%;
    background: #2868e7;
  }
}
