﻿export default [
  {
    path: '/user',
    layout: false,
    routes: [{ path: '/user/login', component: './User/Login' }],
  },
  { path: '/', redirect: '/welcome' },
  { path: '/user/profile', component: './User/Profile', wrappers: ['@/wrappers/auth'] },
  { path: '/welcome', name: '首页', icon: 'HomeOutlined', component: './Welcome', layout: { hideNav: false } },
  { path: '/mysql_detail/:id', name: '数据源', icon: 'DatabaseOutlined', component: './MySQLDetail', wrappers: ['@/wrappers/auth'] },
  { path: '/dashboard', name: '仪表盘', icon: 'DashboardOutlined', component: './Dashboard', wrappers: ['@/wrappers/auth'] },
  { path: '/ai_chat', name: '深度问数', icon: 'RobotOutlined', component: './AiChat', wrappers: ['@/wrappers/auth'] },
  { path: '/mobile', name: '移动端页面', icon: 'MobileOutlined', component: './Mobile', layout: false },
  { path: '/system', name: '系统管理', icon: 'SettingOutlined', component: './System', wrappers: ['@/wrappers/auth'] },
  { path: '/task_queue', name: '任务队列', icon: 'ClockCircleOutlined', component: './TaskQueue', wrappers: ['@/wrappers/auth'] },
  { path: '/privacy', name: '隐私政策', icon: 'SafetyOutlined', component: './Privacy', layout: false },
  { path: '/terms', name: '用户协议', icon: 'FileTextOutlined', component: './Terms', layout: false },
  { path: '*', layout: false, component: './404' },
];
