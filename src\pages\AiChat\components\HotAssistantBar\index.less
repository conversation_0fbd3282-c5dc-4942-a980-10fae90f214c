.hot-assistant-bar-container {
  padding-top: 12px;
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fafafa;
}
.hot-assistant-bar {
  max-width: 110px;
  min-width: 96px;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 6px;
  box-shadow: 0px 0px 6px 0px rgba(8, 31, 50, 0.08);

  .hot-assistant-btn {
    width: 100%;
    margin-bottom: 8px;
    padding: 4px 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;
    transition: background 0.2s;
    .hot-assistant-icon {
      width: 48px;
      height: 48px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
    }
    span {
      width: 100%;
      font-size: 12px;
      line-height: 2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
    }
    &.active {
      background: #e6f7ff;
    }
  }
  .task-queue-btn {
    margin-top: auto;
    width: 100%;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f5f7fa;
    border-radius: 8px;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
      margin-right: 3px;
    }
    span {
      font-size: 12px;
      color: #1677ff;
    }
  }
}
