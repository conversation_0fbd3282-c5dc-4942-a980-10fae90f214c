// 任务队列相关接口
import { request } from '@umijs/max';

// 获取用户任务队列列表（分页）
export async function getTaskQueue(body: { current: number; pageSize: number }, options?: { [key: string]: any }) {
  return request('/admin/Ai/get/taskQueue', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

// 刷新任务进度
export async function refreshTaskQueue(body: { taskId: string }, options?: { [key: string]: any }) {
  return request('/admin/Ai/refresh/taskQueue', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
