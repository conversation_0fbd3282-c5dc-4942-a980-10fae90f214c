// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 管理员创建用户
 * 创建新的用户账号，包含用户基本信息
 * @param body 用户创建请求参数
 * @param options 请求配置选项
 * @returns 创建成功返回用户ID
 */
export async function addUser(body: API.UserAddRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseLong>('/admin/user/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 管理员删除用户账号
 * 根据用户ID删除指定的用户账号
 * @param ids 要删除的用户ID数组
 * @param options 请求配置选项
 * @returns 删除成功返回true
 */
export async function deleteUser(ids: string[], options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: ids,
    ...(options || {}),
  });
}

/**
 * 根据用户ID获取用户信息
 * 获取指定用户的详细信息
 * @param params 查询参数，包含用户ID
 * @param options 请求配置选项
 * @returns 返回用户详细信息
 */
export async function getUserById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUserByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.BaseResponseUser>('/admin/user/get', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * 获取当前登录用户信息
 * 获取当前登录用户的详细信息
 * @param options 请求配置选项
 * @returns 返回当前登录用户信息
 */
export async function getLoginUser(options?: { [key: string]: any }) {
  return request<API.BaseResponseLoginUserVO>('/admin/user/get/login', {
    method: 'GET',
    ...(options || {}),
  });
}

/**
 * 管理员获取用户分页列表
 * 获取用户列表，支持分页查询
 * @param body 分页查询请求参数
 * @param options 请求配置选项
 * @returns 返回用户分页列表
 */
export async function listUserByPage(body: API.UserQueryRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponsePageUser>('/admin/user/list/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 用户登录
 * 用户通过账号密码进行登录认证
 * @param body 登录请求参数，包含账号和密码
 * @param options 请求配置选项
 * @returns 登录成功返回用户信息
 */
export async function userLogin(body: API.UserLoginRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseLoginUserVO>('/admin/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 用户注销
 * 用户退出登录，清除登录状态
 * @param options 请求配置选项
 * @returns 注销成功返回true
 */
export async function userLogout(options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/logout', {
    method: 'POST',
    ...(options || {}),
  });
}

/**
 * 用户更新个人信息
 * 用户修改自己的个人信息
 * @param body 个人信息更新请求参数
 * @param options 请求配置选项
 * @returns 更新成功返回true
 */
export async function updateMyUser(body: API.UserUpdateMyRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/update/my', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 管理员修改用户信息（包含扩展字段）
 * 管理员修改用户的完整信息，包括姓名、头像、角色、联系方式、科室、职位等
 * @param body 用户信息更新请求参数
 * @param options 请求配置选项
 * @returns 更新成功返回true
 */
export async function updateUserByAdmin(body: API.UserUpdateByAdminRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/re/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 用户修改密码
 * 用户通过旧密码验证后修改新密码
 * @param body 密码修改请求参数，包含用户ID、旧密码、新密码
 * @param options 请求配置选项
 * @returns 修改成功返回true
 */
export async function updateUserPassword(body: API.UserUpdatePasswordRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/update/password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 管理员重置用户密码
 * 管理员直接重置指定用户的密码，无需旧密码验证
 * @param body 密码重置请求参数，包含用户ID和新密码
 * @param options 请求配置选项
 * @returns 重置成功返回true
 */
export async function resetUserPassword(body: API.UserResetPasswordRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/reset/password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 管理员启用/禁用用户账号
 * 管理员控制用户账号的启用或禁用状态
 * @param body 状态更新请求参数，包含用户ID和状态值（0-禁用，1-正常）
 * @param options 请求配置选项
 * @returns 状态更新成功返回true
 */
export async function updateUserStatus(body: API.UserUpdateStatusRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/update/status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 用户修改联系方式
 * 用户更新自己的联系电话
 * @param body 联系方式更新请求参数，包含用户ID和新的联系电话
 * @param options 请求配置选项
 * @returns 更新成功返回true
 */
export async function updateUserTel(body: API.UserUpdateTelRequest, options?: { [key: string]: any }) {
  return request<API.BaseResponseBoolean>('/admin/user/update/tel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/**
 * 批量注册用户
 * 通过文件批量导入用户账号
 * @param file 用户数据文件
 * @param options 请求配置选项
 * @returns 批量注册成功返回结果
 */
export async function importUsers(file: File, options?: { [key: string]: any }) {
  const formData = new FormData();
  formData.append('file', file);

  return request<API.BaseResponseBoolean>('/admin/user/import', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: formData,
    ...(options || {}),
  });
}

/**
 * 批量导入模板下载
 * 下载用户批量导入的模板文件
 * @param options 请求配置选项
 * @returns 返回模板文件
 */
export async function downloadUserTemplate(options?: { [key: string]: any }) {
  return request<Blob>('/admin/user/template', {
    method: 'GET',
    responseType: 'blob',
    ...(options || {}),
  });
}
