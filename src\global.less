html,
body,
#root {
  height: 100%;
  min-width: 1200px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  line-height: 1.5715;
  overflow: hidden;
  scroll-behavior: smooth;
}

:root {
  --layout-height: 100%;
}

.ant-design-pro,
.ant-layout,
.ant-pro-layout-container,
.ant-pro-layout-content,
.ant-app {
  height: var(--layout-height);
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

// 横向滚动条特定样式
::-webkit-scrollbar:horizontal {
  height: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track:horizontal {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

/* fadeIn.css */

/* 定义渐入动画 */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 应用渐入动画的类 */
.fade-in {
  opacity: 0; /* 初始状态为透明 */
  animation: fadeIn 1s ease-out forwards; /* 动画持续时间为2秒 */
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.ant-pro-layout .ant-pro-layout-header,
.ant-layout-header {
  height: 72px !important;
  line-height: 72px !important;
}

.ant-pro-layout .ant-pro-layout-content {
  height: 100%;
  padding-block: 0;
  padding-inline: 0;
}

.ant-pro-layout .ant-layout-header.ant-pro-layout-header {
  background-color: transparent;
  backdrop-filter: none;
  border-block-end: none;
}

:where(.css-dev-only-do-not-override-1d4w9r2).ant-btn-variant-solid:not(.disabled):not(.ant-btn-disabled):hover {
  background: linear-gradient(90deg, #206dfd 0%, #1692ff 50.75%, #00b2fe 100%);
  color: #fff;
}

/* 移动设备适配 */
@media screen and (max-width: 768px) {
  html.mobile-device #root,
  body.mobile-device #root {
    max-width: 100vw;
    min-width: unset;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch; /* 增加 iOS 滚动流畅度 */
    .ant-layout-header {
      display: none;
    }
  }
}

// 定义markdown样式
.markdown-body {
  table {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    table-layout: fixed;
  }
  th,
  td {
    word-break: break-all;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 标题样式
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    line-height: 1.4;
    margin: 1.5em 0 0.8em;
    color: #1a202c;
    letter-spacing: -0.025em;
  }

  h1 {
    font-size: 2em;
  }
  h2 {
    font-size: 1.75em;
  }
  h3 {
    font-size: 1.5em;
  }
  h4 {
    font-size: 1.25em;
  }
  h5 {
    font-size: 1.1em;
  }
  h6 {
    font-size: 1em;
  }

  // 段落
  p {
    margin: 0.8em 0;
    line-height: 1.8;
  }

  // 列表
  ul,
  ol {
    margin: 0.8em 0 0.8em 1.5em;
    padding: 0;
    list-style-position: outside;
  }

  ul {
    list-style-type: disc;
  }
  ol {
    list-style-type: decimal;
  }

  li {
    margin: 0.4em 0;
    line-height: 1.8;
    padding-left: 0.3em;
  }

  // 强调
  strong {
    font-weight: 600;
    color: #1a202c;
  }

  em {
    font-style: italic;
    color: #4a5568;
  }

  // 引用
  blockquote {
    margin: 1.2em 0;
    padding: 0.8em 1.2em;
    border-left: 4px solid #4299e1;
    background-color: #f7fafc;
    color: #4a5568;
    font-style: italic;
    border-radius: 0 4px 4px 0;

    p {
      margin: 0;
    }
  }

  // 链接
  a {
    color: #3182ce;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      color: #2c5282;
      border-bottom-color: #2c5282;
    }
  }

  // 行内代码
  code {
    background-color: #f7fafc;
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9em;
    color: #e53e3e;
    border: 1px solid #e2e8f0;
  }

  // 代码块
  pre {
    background-color: #2d3748;
    color: #e2e8f0;
    padding: 1.2em;
    overflow: auto;
    border-radius: 8px;
    margin: 1.2em 0;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9em;
    line-height: 1.6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    code {
      background: none;
      padding: 0;
      color: inherit;
      border: none;
      font-size: inherit;
    }
  }

  // 水平线
  hr {
    border: none;
    border-top: 2px solid #e2e8f0;
    margin: 2em 0;
  }

  // 图片
  img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1.2em 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  // 表格
  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);

    th,
    td {
      border: 1px solid #e2e8f0;
      padding: 10px 14px;
      text-align: center;
      font-size: 14px;
      vertical-align: middle;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    th {
      background: #f4f6f7;
      font-weight: 600;
      color: #2d3748;
      white-space: nowrap;
      letter-spacing: 1px;
    }

    td {
      color: #4a5568;
      background: #fff;
    }

    tr:nth-child(even) td {
      background: #f8fafc;
    }

    tr:hover td {
      background: #e6f7ff;
    }
  }

  // 任务列表
  input[type='checkbox'] {
    margin-right: 0.5em;
  }

  // 数学公式
  .math {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0.5em 0;
  }

  // 高亮文本
  mark {
    background-color: #fefcbf;
    padding: 0.2em 0.4em;
    border-radius: 2px;
  }

  // 删除线
  del {
    color: #a0aec0;
  }

  // 上标和下标
  sup,
  sub {
    font-size: 0.75em;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sup {
    top: -0.5em;
  }

  sub {
    bottom: -0.25em;
  }
}

:root {
  --layout-height: 100%;
}

.ant-design-pro,
.ant-layout,
.ant-pro-layout-container,
.ant-pro-layout-content,
.ant-app {
  height: var(--layout-height);
}

// 覆盖 Ant Design 表格表头背景色
.ant-table-thead > tr > th {
  background: #f4f6f7 !important;
}

.ant-form-item {
  margin-bottom: 16px !important;
}
.common-modal {
  .ant-modal-content {
    padding: 0;
    padding-bottom: 36px;
    border-radius: 12px;
    overflow: hidden;
  }

  .ant-modal-header {
    display: flex;
    align-items: center;
    height: 54px;
    padding: 0 24px;
    border-bottom: 1px solid #d1d5db;
    margin-bottom: 0;
    .ant-modal-title {
      color: #2868e7;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    display: flex;
    justify-content: center;
    margin-top: 0;
  }
}
