import { <PERSON><PERSON>, Card, Carousel, Col, Row, theme } from 'antd';
import { UpOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import './index.less';
import { useNavigate } from 'react-router-dom';
import SequenceFrame from '@/components/SequenceFrame';
import frame0_1 from '../../../public/sequence_1/0.png';
import frame1_1 from '../../../public/sequence_1/1.png';
import frame2_1 from '../../../public/sequence_1/2.png';
import frame3_1 from '../../../public/sequence_1/3.png';
import frame4_1 from '../../../public/sequence_1/4.png';
import frame5_1 from '../../../public/sequence_1/5.png';
import frame6_1 from '../../../public/sequence_1/6.png';
import frame7_1 from '../../../public/sequence_1/7.png';
import frame8_1 from '../../../public/sequence_1/8.png';
import frame9_1 from '../../../public/sequence_1/9.png';

import frame0_2 from '../../../public/sequence_2/0.png';
import frame1_2 from '../../../public/sequence_2/1.png';
import frame2_2 from '../../../public/sequence_2/2.png';
import frame3_2 from '../../../public/sequence_2/3.png';
import frame4_2 from '../../../public/sequence_2/4.png';
import frame5_2 from '../../../public/sequence_2/5.png';
import frame6_2 from '../../../public/sequence_2/6.png';
import frame7_2 from '../../../public/sequence_2/7.png';
import frame8_2 from '../../../public/sequence_2/8.png';
import frame9_2 from '../../../public/sequence_2/9.png';

import frame0_3 from '../../../public/sequence_3/0.png';
import frame1_3 from '../../../public/sequence_3/1.png';
import frame2_3 from '../../../public/sequence_3/2.png';
import frame3_3 from '../../../public/sequence_3/3.png';
import frame4_3 from '../../../public/sequence_3/4.png';
import frame5_3 from '../../../public/sequence_3/5.png';
import frame6_3 from '../../../public/sequence_3/6.png';
import frame7_3 from '../../../public/sequence_3/7.png';
import frame8_3 from '../../../public/sequence_3/8.png';
import frame9_3 from '../../../public/sequence_3/9.png';
import { useInView } from 'react-intersection-observer';
import { AnimatePresence } from 'framer-motion';

const visualizationData = [
  {
    title: '智析数据，图示报告',
    subtitle: 'AI可视化',
    subdesc: '让分析易上手更迅速',
    desc: '数据可视化通过将抽象数据转化为图形化语言，显著提升了人类对复杂信息的认知效率,通过数据可视化,可以直观发现、分析、预警数据中所隐藏的问题,及时应对业务中的风险,发现增长点.',
    img: '/assets/29vbh75s.svg',
    icon: '/assets/f8ux3c57.svg',
  },
  {
    title: '智析数据，图示报告',
    subtitle: 'AI辅助',
    subdesc: '交互式挖掘数据价值',
    desc: '以上传的数据为基准,随问随答的方式更快速、更准确地从大量数据中提取有价值的信息,AI对话式辅助分析通过自然语言交互重塑数据分析范式，其核心优势在于将复杂分析能力转化为零门槛的智能服务。系统通过语义理解引擎自动解析用户意图，结合知识图谱构建动态分析路径.',
    img: '/assets/erbjo0nf.svg',
    icon: '/assets/f8ux3c57.svg',
  },
  {
    title: '智析数据，图示报告',
    subtitle: '分析助手',
    subdesc: '创建属于自己的ai分析师',
    desc: '自定义您的专属AI数据分析师，智能解析业务脉络。支持多维度建模、自动化归因和口语化交互，将庞杂信息转化为精准决策建议。零代码操作界面让业务人员无需专业背景即可构建分析模型，用个性化数据洞察引擎，释放企业隐藏的数据金矿.',
    img: '/assets/ybdg5aa5.svg',
    icon: '/assets/f8ux3c57.svg',
  },
];

const Welcome: React.FC = () => {
  const { token } = theme.useToken();
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const navigate = useNavigate();
  const [activeIndex, setActiveIndex] = useState(0);
  const [ref0, inView0] = useInView({ threshold: 0.5 });
  const [ref1, inView1] = useInView({ threshold: 0.5 });
  const [ref2, inView2] = useInView({ threshold: 0.5 });

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (inView0) setActiveIndex(0);
    else if (inView1) setActiveIndex(1);
    else if (inView2) setActiveIndex(2);
  }, [inView0, inView1, inView2]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const handleLearnMore = () => {
    navigate('/dashboard');
  };

  // 产品跳转处理函数
  const handleProductNavigation = (productName: string) => {
    switch (productName) {
      case '深度问数':
        window.open('http://wenshu.aethermind.cn', '_blank');
        break;
      case '训推一体化方案':
        window.open('http://lab.aethermind.cn', '_blank');
        break;
      default:
        // 其他产品不进行跳转
        break;
    }
  };

  // 智能分析序列帧图片
  const analysisFrames1 = [frame0_1, frame1_1, frame2_1, frame3_1, frame4_1, frame5_1, frame6_1, frame7_1, frame8_1, frame9_1];
  const analysisFrames2 = [frame0_2, frame1_2, frame2_2, frame3_2, frame4_2, frame5_2, frame6_2, frame7_2, frame8_2, frame9_2];
  const analysisFrames3 = [frame0_3, frame1_3, frame2_3, frame3_3, frame4_3, frame5_3, frame6_3, frame7_3, frame8_3, frame9_3];

  return (
    <div className="welcome-container">
      <div className="data-analysis-platform">
        <video className="background-video" autoPlay muted loop playsInline>
          <source src="/assets/1g3vj955.mp4" type="video/mp4" />
          您的浏览器不支持视频播放。
        </video>
        <div className="hero-section">
          <motion.div
            className="hero-content"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.5,
              ease: 'easeOut',
            }}
          >
            <img className="main-title" src="/assets/x5y6k0x1.svg" />
            <h2 className="sub-title">数据治理&分析</h2>
            <p className="description">让分析易如反掌！</p>
            <p className="description">借助LLM大模型 + Agent，快速生成可视化分析图表。</p>
            <button
              className="action-button"
              onClick={() => {
                document.getElementById('visualization-section')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              AI数据可视化分析工具
              <img className="button-icon" src="/assets/9v5mm1m2.svg" />
            </button>
          </motion.div>
        </div>
      </div>

      <div className="app-container-wrapper"></div>
      <div className="app-container-decoration"></div>

      <motion.div
        className="app-container"
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.5,
          ease: 'easeOut',
        }}
      >
        <header className="header-section">
          <h1 className="header-title">欢迎使用 深度问数</h1>
          <p className="header-description">深度问数为用户分析大量数据，帮助您在数据中发现有价值的信息。</p>
        </header>

        <section className="features-section">
          <article
            className="feature-card"
            onMouseEnter={() => setHoveredCard(0)}
            onMouseLeave={() => setHoveredCard(null)}
            onClick={() => navigate('/dashboard')}
            style={{ cursor: 'pointer' }}
          >
            <div className="feature-icon-wrapper">
              <SequenceFrame images={analysisFrames1} play={hoveredCard === 0} />
            </div>
            <h3 className="feature-title">智能分析</h3>
            <p className="feature-description">一键上传数据，即可获取数据的智能分析报告。</p>
          </article>

          <div className="feature-divider"></div>

          <article
            className="feature-card"
            onMouseEnter={() => setHoveredCard(1)}
            onMouseLeave={() => setHoveredCard(null)}
            onClick={() => navigate('/ai_chat')}
            style={{ cursor: 'pointer' }}
          >
            <div className="feature-icon-wrapper">
              <SequenceFrame images={analysisFrames2} play={hoveredCard === 1} />
            </div>
            <h3 className="feature-title">AI辅助分析</h3>
            <p className="feature-description">AI加持，支持随问随答，更快速、更准确地从大量数据中提取有价值的信息。</p>
          </article>

          <div className="feature-divider"></div>

          <article
            className="feature-card"
            onMouseEnter={() => setHoveredCard(2)}
            onMouseLeave={() => setHoveredCard(null)}
            onClick={() => navigate('/ai_chat')}
            style={{ cursor: 'pointer' }}
          >
            <div className="feature-icon-wrapper">
              <SequenceFrame images={analysisFrames3} play={hoveredCard === 2} />
            </div>
            <h3 className="feature-title">创建分析助手</h3>
            <p className="feature-description">创建属于自己的AI数据分析助手。</p>
          </article>
        </section>
      </motion.div>

      {/* 三个滚动触发区块（空div用于触发inView） */}
      {[ref0, ref1, ref2].map((ref, idx) => (
        <div className="ai-visualization-container" ref={ref} key={idx} style={{ minHeight: 300, position: 'relative' }}>
          <AnimatePresence mode="wait">
            {activeIndex === idx && (
              <motion.div
                key={activeIndex}
                initial={{ opacity: 0, y: 60 }}
                animate={{ opacity: 1, y: 0, transition: { type: 'spring', damping: 24, stiffness: 120 } }}
                exit={{ opacity: 0, y: 60, transition: { duration: 0 } }}
                style={{ position: 'absolute', width: '100%' }}
              >
                <div className="visualization-content">
                  {idx === 1 ? (
                    <div className="img-section" style={{ backgroundColor: 'transparent' }}>
                      <img
                        src={visualizationData[idx].img}
                        style={{
                          position: 'relative',
                          width: '100%',
                          height: 'auto',
                          top: '0',
                          right: '0',
                          zIndex: 1,
                        }}
                      />
                    </div>
                  ) : (
                    <div className="img-section">
                      <img src={visualizationData[idx].img} />
                    </div>
                  )}
                  <div className="img-section-text">
                    <h1>{visualizationData[idx].title}</h1>
                    <p>更好扽理解数据的含义和价值,提高决策的准确性和效率</p>
                  </div>
                  <div className="text-section">
                    <img src={visualizationData[idx].icon} />
                    <h1 className="title">{visualizationData[idx].subtitle}</h1>
                    <h2 className="subtitle">{visualizationData[idx].subdesc}</h2>
                    <div className="description">
                      <p>{visualizationData[idx].desc}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}

      <div className="contact-info-container">
        <div className="contact-info-inner">
          {/* Logo 区域 */}
          <div className="footer-logo-area">
            <img className="logo" src="/assets/xiledgnd.svg" alt="汇智灵曦" />
          </div>
          {/* 产品 */}
          <div className="footer-col">
            <div className="footer-title">产品</div>
            <div className="footer-product-item" onClick={() => handleProductNavigation('深度问数')}>
              深度问数
            </div>
            <div>灵曦助手</div>
            <div className="footer-product-item" onClick={() => handleProductNavigation('训推一体化方案')}>
              训推一体化方案
            </div>
            <div>智慧影像</div>
            <div>病理交互式大模型</div>
          </div>
          {/* 法律&安全 */}
          <div className="footer-col">
            <div className="footer-title">法律&安全</div>
            <div className="footer-product-item" onClick={() => window.open('/privacy', '_blank')}>
              隐私政策
            </div>
            <div className="footer-product-item" onClick={() => window.open('/terms', '_blank')}>
              用户协议
            </div>
            <div>反馈安全漏洞</div>
          </div>
          {/* 加入我们 */}
          <div className="footer-col">
            <div className="footer-title">加入我们</div>
            <div>公司详情</div>
          </div>
        </div>
        {/* 备案号 */}
        <div className="footer-beian">
          <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">
            沪ICP备2025134180号
          </a>
        </div>
      </div>

      {showScrollTop && (
        <Button
          type="primary"
          shape="circle"
          size="large"
          onClick={scrollToTop}
          style={{
            position: 'fixed',
            right: '20px',
            bottom: '20px',
            zIndex: 1000,
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          }}
          icon={<UpOutlined />}
        />
      )}
    </div>
  );
};

export default Welcome;
