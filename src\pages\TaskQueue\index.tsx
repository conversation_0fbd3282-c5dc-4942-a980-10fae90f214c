import React, { useEffect, useState } from 'react';
import { Button, Tag, Tooltip, Select, Space, message, Spin, Pagination } from 'antd';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import './index.less';
import { getTaskQueue, refreshTaskQueue } from '@/services/DataLoom/taskQueueController';
import { useNavigate } from 'react-router-dom';

const statusMap = {
  running: { text: '进行中', color: '#409EFF', tagColor: 'processing' },
  completed: { text: '已完成', color: '#67C23A', tagColor: 'success' },
  failed: { text: '已失败', color: '#F56C6C', tagColor: 'error' },
};

const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '进行中', value: 'running' },
  { label: '已完成', value: 'completed' },
  { label: '已失败', value: 'failed' },
];

const PAGE_SIZE = 10;

const TaskQueueCard: React.FC = () => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [total, setTotal] = useState(0);
  const [status, setStatus] = useState('all');

  // 获取任务队列
  const fetchTasks = async (page = current, size = pageSize, statusFilter = status) => {
    setLoading(true);
    try {
      const body: any = { current: page, pageSize: size };
      if (statusFilter !== 'all') body.status = statusFilter;
      const res = await getTaskQueue(body);
      setTasks(res?.data?.records || []);
      setTotal(res?.data?.total || 0);
    } catch (e) {
      message.error('获取任务队列失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks(1, pageSize, status);
    // eslint-disable-next-line
  }, [status, pageSize]);

  // 分页切换
  const handlePageChange = (page: number, size?: number) => {
    setCurrent(page);
    if (size && size !== pageSize) setPageSize(size);
    fetchTasks(page, size || pageSize, status);
  };

  // 状态筛选
  const handleStatusChange = (value: string) => {
    setStatus(value);
    setCurrent(1);
    fetchTasks(1, pageSize, value);
  };

  // 刷新单个任务状态
  const handleRefreshTask = async (taskId: string) => {
    setLoading(true);
    try {
      await refreshTaskQueue({ taskId });
      message.success('刷新成功');
      fetchTasks(current, pageSize, status);
    } catch (e) {
      message.error('刷新失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新列表
  const handleRefreshList = () => {
    fetchTasks(current, pageSize, status);
  };

  return (
    <div className="task-queue-container">
      <div className="task-queue-content">
        <div className="task-queue-header">
          <div className="header-left">
            <img className="header-icon" src="/assets/image_1752030619419_fa174z.svg" alt="任务队列" onClick={() => navigate('/')} />
            <span className="header-title">任务队列</span>
          </div>
          <div className="header-right">
            <Select value={status} style={{ width: 120 }} onChange={handleStatusChange} options={statusOptions} />
            <Button icon={<ReloadOutlined />} type="default" className="task-queue-refresh-button" onClick={handleRefreshList}>
              刷新列表
            </Button>
            <img src="/assets/image_1752046366874_gto6kn.svg" alt="排序" style={{ cursor: 'pointer' }} />
            <span style={{ color: '#262628' }}>共{total}个任务</span>
          </div>
        </div>
        <Spin spinning={loading} tip="加载中...">
          <div className="task-queue-list">
            {tasks.length === 0 ? (
              <div style={{ textAlign: 'center', color: '#999', padding: 40 }}>暂无任务</div>
            ) : (
              tasks.map((task, idx) => (
                <div className={`task-card${task.status === 'running' ? ' running' : ''}`} key={task.id || idx}>
                  <div className="task-card-header">
                    {task.isNew && <span className="task-new">新</span>}
                    <span className="task-title">{task.title}</span>
                    <div className="task-card-actions">
                      <Tooltip title="刷新状态">
                        <Button
                          icon={<ReloadOutlined />}
                          type="primary"
                          ghost
                          size="middle"
                          className="task-card-refresh-button"
                          style={{ marginRight: 8 }}
                          onClick={() => handleRefreshTask(task.id)}
                        >
                          刷新状态
                        </Button>
                      </Tooltip>
                      <Tooltip title="查询结果">
                        <Button
                          icon={<SearchOutlined />}
                          type="primary"
                          size="middle"
                          className="task-card-result-button"
                          disabled={task.status !== 'completed'}
                        >
                          查询结果
                        </Button>
                      </Tooltip>
                    </div>
                  </div>
                  <Space direction="horizontal" className="task-card-body">
                    <div className="task-info-left">
                      <div className="task-info-left-item">
                        <span className="task-info-left-title">任务ID：</span>
                        {task.id}
                      </div>
                      <div className="task-info-left-item">
                        <span className="task-info-left-title">开始时间：</span>
                        {task.startTime}
                      </div>
                    </div>
                    <div className="task-info-center">
                      <div className="task-info-center-item">
                        <span className="task-info-center-title">会话ID：</span>
                        {task.sessionId}
                      </div>
                      <div className="task-info-center-item">
                        <span className="task-info-center-title">结束时间：</span>
                        {task.endTime || '——'}
                      </div>
                    </div>
                    <div className="task-info-right">
                      <div className="task-info-right-item">
                        <span className="task-info-right-title">任务状态：</span>
                        <Tag className={`task-card-status-tag ${task.status}`}>
                          {statusMap[task.status as 'running' | 'completed' | 'failed']?.text || task.status}
                        </Tag>
                      </div>
                    </div>
                  </Space>
                </div>
              ))
            )}
          </div>
          <div style={{ textAlign: 'right', margin: '16px 0' }}>
            {total > 0 && (
              <Pagination
                current={current}
                pageSize={pageSize}
                total={total}
                showSizeChanger
                pageSizeOptions={[5, 10, 20, 50]}
                onChange={handlePageChange}
                onShowSizeChange={handlePageChange}
                showTotal={(t) => `共${t}条`}
              />
            )}
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default TaskQueueCard;
