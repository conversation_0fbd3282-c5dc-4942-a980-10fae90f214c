import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

interface ExportToPDFOptions {
  element: HTMLElement;
  filename: string;
  title?: string;
  quality?: number;
}

export const exportToPDF = async ({ element, filename, title, quality = 2 }: ExportToPDFOptions): Promise<boolean> => {
  try {
    const canvas = await html2canvas(element, {
      scale: quality,
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
    });

    const imgData = canvas.toDataURL('image/jpeg', 1.0);

    const pxToMm = (px: number) => px * 0.264583;

    const imgWidth = pxToMm(canvas.width);
    const imgHeight = pxToMm(canvas.height);

    const margin = 10;
    const titleOffset = title ? 40 : 0;

    const pdfWidth = imgWidth + margin * 2;
    const pdfHeight = imgHeight + margin * 2 + titleOffset;

    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: [pdfWidth, pdfHeight],
    });

    if (title) {
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(48);
      pdf.setTextColor(0, 0, 0);

      const titleWidth = pdf.getTextWidth(title);
      const titleX = (pdfWidth - titleWidth) / 2;

      pdf.text(title, titleX, margin + 20);

      pdf.setDrawColor(100, 100, 100);
      pdf.setLineWidth(0.5);
      pdf.line(margin, margin + 30, pdfWidth - margin, margin + 30);
    }

    const yOffset = margin + titleOffset;

    pdf.addImage(imgData, 'JPEG', margin, yOffset, imgWidth, imgHeight);

    pdf.save(filename);

    return true;
  } catch (error) {
    console.error('PDF导出错误:', error);
    return false;
  }
};
