.mysql-detail {
  width: 100%;
  height: calc(100% + 72px);
  margin-top: -72px;
  background-color: rgb(255, 255, 255);
  overflow-x: hidden;
  overflow-y: auto;

  .mysql-content {
    height: 100%;
    display: flex;

    .mysql-section {
      flex: 1;
      min-width: 0;
    }
  }

  .upload-section {
    height: 327px;
    padding-top: 138px;
    background: url('/assets/de7ndlnl.svg') no-repeat;
    background-size: cover;

    .ant-space {
      width: 100%;
      justify-content: space-around;

      .ant-card {
        width: 390px;
        height: 123px;
        overflow: hidden;
        cursor: pointer;
        border-radius: 16px;
        border: 1px solid #fff;
        background: rgba(255, 255, 255, 0.7);
        box-shadow: 0px 0px 6px 0px rgba(21, 99, 132, 0.2);
        backdrop-filter: blur(2px);

        .ant-card-body {
          height: 100%;
          padding: 0;
        }

        .card-content {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 32px;

          .icon-section {
            margin-right: 24px;

            img {
              width: 54px;
              height: 43px;
              object-fit: contain;
            }
          }

          .text-section {
            text-align: left;

            h3 {
              color: #2868e7;
              font-size: 16px;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .data-list-card {
    background: #fff;
    border-radius: 8px;

    .ant-card-head {
      padding: 0 24px;
      border-bottom: none;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 400;
        color: #262628;
      }
    }

    .ant-card-body {
      padding: 24px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .card-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .search-bar {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }
    }
  }

  .ant-table-wrapper {
    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #1e2329;
        border-bottom: 1px solid #f0f0f0;
        padding: 0 16px;
        height: 46px;
        line-height: 46px;

        .ant-checkbox-wrapper {
          .ant-checkbox {
            .ant-checkbox-inner {
              border-radius: 2px;
              border-color: #d9d9d9;
            }
          }
        }
      }

      .ant-table-tbody > tr {
        height: 46px;

        &.custom-row {
          height: 46px;

          td {
            padding: 0 16px;
            height: 46px;
            line-height: 46px;
          }

          .ant-checkbox-wrapper {
            .ant-checkbox {
              .ant-checkbox-inner {
                border-radius: 2px;
                border-color: #d9d9d9;
              }
            }
          }
        }
      }

      .ant-table-cell {
        .ant-btn-link {
          padding: 4px 0;
          height: 24px;
          font-size: 14px;

          &:hover {
            color: #40a9ff;
            background: transparent;
          }

          &.ant-btn-dangerous {
            color: #ff4d4f;

            &:hover {
              color: #ff7875;
              background: transparent;
            }
          }
        }
      }
    }

    .ant-table-pagination {
      margin: 16px 0;
    }
  }

  :global {
    .ant-drawer-body {
      padding: 0;
      height: calc(100% - 55px);
      overflow: hidden;
    }

    .ant-drawer-header {
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-drawer-header-title {
      flex-direction: row-reverse;
    }

    .ant-drawer-close {
      margin-right: 0;
      margin-left: 12px;
    }
  }
}

.preview-btn {
  color: #2868e7 !important;
}

.update-btn {
  color: #0dc0e3 !important;
}

.clear-btn {
  color: #e36024 !important;
}

.custom-info-modal .ant-modal-confirm-body {
  display: flex;
  align-items: flex-start;
  padding: 24px;
}

.custom-info-modal .ant-modal-confirm-body-wrapper {
  padding: 0;
}

.custom-info-modal .ant-modal-confirm-btns {
  margin-top: 24px;
}
