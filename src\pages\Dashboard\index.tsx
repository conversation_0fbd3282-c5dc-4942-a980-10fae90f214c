'use client';

import React, { useEffect, useState, useMemo, useRef } from 'react';
import './index.less';
import { SearchOutlined, InfoCircleOutlined, DeleteOutlined, MoreOutlined, EditOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import { Button, Input, Tooltip, Popconfirm, Dropdown, Modal, App } from 'antd';
import ReactEcharts from 'echarts-for-react';
import type { EChartsCoreOption, EChartsType } from 'echarts/core';
import type { SeriesOption, BarSeriesOption, LineSeriesOption, PieSeriesOption, ScatterSeriesOption } from 'echarts/types/dist/shared';
import { applyChartSettings } from '@/utils/chartSettings';
import ChartContainer from './components/ChartContainer';
import AddChartModal from './components/AddChartModal';
import AddDashboardModal from '@/components/AddDashboardModal';
import { CHART_TYPES } from '@/config/charts';
import {
  listChartByDashboardId,
  listAllDashboard,
  deleteDashboard,
  getDashboardById,
  getDatasourceById,
} from '@/services/DataLoom/yibiaopanjiekou';
import 'echarts-wordcloud';

interface chartType {
  id: string;
  chartName: string;
  chartType: string;
  component: React.ReactElement;
  chartOption: any;
  analysisLastFlag: boolean;
  analysisRes?: string;
  dataOption?: any;
  chartStyle?: any;
}

interface ChartTypeConfig {
  id: string;
  name: string;
  icon: string;
}

// 图表类型配置数组，从配置文件转换
const chartTypes: ChartTypeConfig[] = CHART_TYPES.map((chart) => ({
  id: chart.id,
  name: chart.name,
  icon: chart.image, // 将 image 字段映射为 icon
}));

const DataDashboard: React.FC = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const [datasourceId, setDatasourceId] = useState<string>('');
  const [datasourceType, setDatasourceType] = useState<string>('');
  const [configuration, setConfiguration] = useState<string>('');
  const [dashboardId, setDashboardId] = useState<string>('');
  const [isAnalyzeEnabled, setIsAnalyzeEnabled] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  const [addChartModalVisible, setAddChartModalVisible] = useState(false);
  const [selectedChart, setSelectedChart] = useState<chartType | null>(null);
  const [addDashboardModalVisible, setAddDashboardModalVisible] = useState(false);
  const [menuOptions, setMenuOptions] = useState<any[]>([]);

  const [layouts, setLayouts] = useState(undefined);
  const [charts, setCharts] = useState<chartType[]>([]);
  const renderCharts = useMemo(() => charts, [charts]);

  const chartRef = useRef<any>(null);

  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);

  const updateChartOption = (newSettings: ThemeConfigValues) => {
    if (!selectedChart) return;

    const currentOption = selectedChart.chartOption;

    // 使用封装的工具函数应用设置
    const newOption = applyChartSettings(currentOption, newSettings, selectedChart.chartType);

    setSelectedChart((prev) =>
      prev
        ? {
            ...prev,
            chartOption: newOption,
            chartStyle: newSettings,
          }
        : null,
    );
  };

  const handleSettingsChange = (settings: ThemeConfigValues) => {
    updateChartOption(settings);
  };

  const handleAddChart = () => {
    setAddChartModalVisible(false);
    loadCharts();
  };

  const handleChartClick = (chartId: string) => {
    const chart = charts.find((c) => c.id === chartId);
    setSelectedChart(chart || null);
  };

  const handleInfoClick = () => {
    setAddDashboardModalVisible(true);
  };

  const handleDeleteDashboard = async (dashboardId: number) => {
    Modal.confirm({
      title: '删除确认',
      content: '确定要删除这个仪表盘吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await deleteDashboard({ dashboardId });
          if (res.code === 0) {
            message.success('删除成功');
            loadAllDashboard();
          } else {
            message.error(res.message || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleShareDashboard = () => {
    // 分享仪表盘功能
    message.info('分享仪表盘功能开发中...');
  };

  const handleExportDashboard = () => {
    // 导出仪表盘功能
    message.info('导出仪表盘功能开发中...');
  };

  const loadAllDashboard = () => {
    listAllDashboard().then((res) => {
      const { code, data } = res;
      if (code === 0) {
        setMenuOptions(data || []);
        if (data && data.length > 0) {
          setDashboardId(String(data[0]?.id) || '');
          setDatasourceId(String(data[0]?.datasourceId) || '');
        }
      } else {
        message.error(res.message || '获取仪表盘失败');
      }
    });
  };
  const loadCharts = () => {
    if (!dashboardId) {
      return;
    }
    listChartByDashboardId({ dashboardId: dashboardId }).then((res) => {
      const { code, data } = res;
      if (code === 0) {
        // 简化处理，直接使用图表配置
        const processedCharts = data.map((item: any) => ({
          ...item,
          chartOption: item.chartOption ? JSON.parse(item.chartOption) : {},
        }));
        setCharts(processedCharts);
      } else {
        message.error(res.message || '获取图表失败');
      }
    });
  };

  const loadDatasource = () => {
    const params = {
      datasourceId,
    };
    getDatasourceById(params).then((res) => {
      const { code, data } = res;
      if (code === 0) {
        setDatasourceType(data?.type || '');
        setConfiguration(data?.configuration || '');
      }
    });
  };

  useEffect(() => {
    loadAllDashboard();
  }, []);

  useEffect(() => {
    if (dashboardId) {
      loadCharts();
    }
  }, [dashboardId]);
  useEffect(() => {
    if (datasourceId) {
      loadDatasource();
    }
  }, [datasourceId]);

  const getChartOption = useMemo(() => {
    return (chart: chartType) => {
      if (selectedChart?.id === chart.id) {
        return selectedChart.chartOption;
      }
      return chart.chartOption;
    };
  }, [selectedChart]);

  const filteredMenuOptions = useMemo(() => {
    if (!searchKeyword) return menuOptions;
    return menuOptions.filter((option) => option.name.toLowerCase().includes(searchKeyword.toLowerCase()));
  }, [menuOptions, searchKeyword]);

  return (
    <div className="dashboard-wrapper">
      {menuOptions.length > 0 ? (
        <div className="dashboard-container">
          <div className={`sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
            <div className="sidebar-header">
              <span>仪表盘</span>
              <div className="header-actions">
                <img src="/assets/image_1752825292180_px0q6p.svg" alt="info" onClick={handleInfoClick} />
                <img
                  src={isSidebarCollapsed ? '/assets/t5hbz8e9.svg' : '/assets/r2l6w3pe.svg'}
                  alt="collapse"
                  className="collapse-icon"
                  onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                />
              </div>
            </div>
            <div className="search-box">
              {!isSidebarCollapsed ? (
                <Input
                  type="text"
                  placeholder="输入关键词搜索"
                  allowClear
                  suffix={<SearchOutlined />}
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                />
              ) : (
                <div className="collapsed-icons">
                  <img src="/assets/t5hbz8e9.svg" alt="expand" className="icon" onClick={() => setIsSidebarCollapsed(false)} />
                  <img src="/assets/cria3xom.svg" alt="add" className="icon" onClick={() => setAddChartModalVisible(true)} />
                  <img
                    src="/assets/jbq9a3gu.svg"
                    alt="search"
                    style={{ marginLeft: '3px' }}
                    className="icon"
                    onClick={() => setIsSidebarCollapsed(false)}
                  />
                </div>
              )}
            </div>
            {!isSidebarCollapsed && (
              <div className="menu">
                {filteredMenuOptions.map((option, index) => (
                  <div
                    key={`menu-item-${option.id}-${index}`}
                    className={`menu-item ${dashboardId === option.id ? 'active' : ''}`}
                    onClick={() => {
                      setDashboardId(option.id);
                      setDatasourceId(option.datasourceId);
                    }}
                    title={option.name}
                  >
                    <div className="menu-item-container">
                      <img src="/assets/image_1752825291616_d1yw6f.svg" alt="info" />
                      <span className="menu-item-name">{option.name}</span>
                    </div>
                    <Dropdown
                      key={`dropdown-${option.id}-${index}`}
                      menu={{
                        items: [
                          {
                            key: `delete-${option.id}-${index}`,
                            icon: <DeleteOutlined />,
                            label: '删除',
                            danger: true,
                            onClick: () => {
                              handleDeleteDashboard(option.id);
                            },
                          },
                        ],
                      }}
                      trigger={['click']}
                    >
                      <Button type="text" icon={<MoreOutlined />} className="more-button" onClick={(e) => e.stopPropagation()} />
                    </Dropdown>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="main-content">
            {(dashboardId || selectedChart || isRightPanelCollapsed) && (
              <div className="action-buttons">
                <div className="action-buttons-left">
                  {dashboardId && (
                    <Button type="primary" className="add-chart-btn" onClick={() => setAddChartModalVisible(true)}>
                      添加图表
                    </Button>
                  )}
                </div>
                <div className="action-buttons-right">
                  {/* {dashboardId && (
                    <div className="ai-assistant-btn">
                      <img src="/assets/image_1753151211856_cc3fyb.svg" alt="AI助手" />
                    </div>
                  )}
                  {dashboardId && (
                    <Button className="share-btn" onClick={handleShareDashboard}>
                      <img
                        src="/assets/image_1753151212975_tjrf8x.svg"
                        alt="分享"
                        style={{ width: '14px', height: '14px', marginRight: '4px' }}
                      />
                      分享仪表盘
                    </Button>
                  )} */}
                  {isRightPanelCollapsed && (
                    <img
                      src="/assets/t5hbz8e9.svg"
                      alt="collapse"
                      className="collapse-icon"
                      onClick={() => setIsRightPanelCollapsed(!isRightPanelCollapsed)}
                    />
                  )}
                </div>
              </div>
            )}
            <div className="dashboard-grid-container">
              {renderCharts.length > 0 ? (
                <div className="dashboard-grid">
                  {renderCharts.map((chart) => (
                    <div key={`chart-${chart.id}`} className="chart-container" onClick={() => handleChartClick(chart.id)}>
                      <ChartContainer
                        key={`chart-container-${chart.id}`}
                        title={chart.chartName}
                        chartOption={getChartOption(chart)}
                        chartId={chart.id}
                        dashboardId={dashboardId}
                        chartName={chart.chartName}
                        analysisRes={chart.analysisRes}
                        onAnalyze={() => setIsAnalyzeEnabled(!isAnalyzeEnabled)}
                        onRename={() => loadCharts()}
                        onDelete={() => loadCharts()}
                      >
                        <ReactEcharts
                          key={`echarts-${chart.id}`}
                          option={getChartOption(chart)}
                          style={{ height: '100%', width: '100%' }}
                          opts={{ renderer: 'svg' }}
                          notMerge={true}
                          lazyUpdate={false}
                        />
                      </ChartContainer>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="dashboard-empty-charts">
                  <div className="dashboard-empty-charts-content">
                    <h1 className="dashboard-empty-charts-title">选择组件，开始搭建你的数据看板</h1>
                    <p className="dashboard-empty-charts-description">
                      快速插入柱状图、折线图、饼图等多种组件类型，直观展示数据，让业务动态一目了然
                    </p>
                    {/* <div className="dashboard-empty-charts-button-container">
                      <Button className="dashboard-empty-charts-button primary" onClick={() => setAddChartModalVisible(true)}>
                        <img src="/assets/image_1753151212465_2bgtdx.svg" alt="添加" />
                        AI生成
                      </Button>
                    </div> */}

                    <div className="chart-types-grid">
                      {chartTypes.map((chartType) => (
                        <div key={chartType.id} className="chart-type-item">
                          <div className="chart-icon">
                            <img src={chartType.icon} alt={chartType.name} />
                          </div>
                          <span className="chart-name">{chartType.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="dashboard-empty">
          <div className="dashboard-empty-content">
            <h1 className="dashboard-empty-title">添加图表，开始搭建你的数据看板</h1>
            <p className="dashboard-empty-description">快速插入柱状图、折线图、饼图等多种组件类型，直观展示数据，让业务动态一目了然</p>
            <div className="dashboard-empty-actions">
              <Button className="dashboard-empty-button primary" onClick={() => setAddDashboardModalVisible(true)}>
                添加仪表盘
              </Button>
              <Button className="dashboard-empty-button secondary" onClick={() => navigate('/ai_chat')}>
                查看历史记录
              </Button>
            </div>
          </div>
        </div>
      )}

      <AddChartModal
        visible={addChartModalVisible}
        datasourceType={datasourceType}
        configuration={configuration}
        dashboardId={dashboardId}
        datasourceId={datasourceId}
        onCancel={() => setAddChartModalVisible(false)}
        onConfirm={handleAddChart}
      />
      <AddDashboardModal
        visible={addDashboardModalVisible}
        onCancel={() => setAddDashboardModalVisible(false)}
        onSuccess={() => {
          setAddDashboardModalVisible(false);
          loadAllDashboard();
        }}
      />
    </div>
  );
};

export default DataDashboard;
