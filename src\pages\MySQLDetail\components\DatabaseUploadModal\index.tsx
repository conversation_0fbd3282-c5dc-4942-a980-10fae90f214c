'use client';

import type React from 'react';
import { Modal, Input, Form, Button, message, Space } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import './index.less';
import { addDatasource } from '@/services/DataLoom/coreDataSourceController';

const { TextArea } = Input;

interface DatabaseUploadModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: DatabaseFormValues) => void;
}

interface DatabaseFormValues {
  name: string;
  description?: string;
  host: string;
  port: string;
  dataBaseName: string;
  userName: string;
  password: string;
  file: File;
}

const DatabaseUploadModal: React.FC<DatabaseUploadModalProps> = ({ visible, onCancel, onOk }) => {
  const [form] = Form.useForm<DatabaseFormValues>();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
      const formData = new FormData();
      const param = {
        pid: 0,
        name: values?.name,
        type: 'mysql',
        configuration: '',
        description: values?.description,
      };
      param.configuration = JSON.stringify({
        host: values?.host,
        port: values?.port,
        dataBaseName: values?.dataBaseName,
        userName: values?.userName,
        password: values?.password,
      });
      //json部分
      const datasourceDTO = JSON.stringify(param);
      formData.append('datasourceDTO', new Blob([datasourceDTO], { type: 'application/json' }));
      const res = await addDatasource(formData);
      if (res.code === 0) {
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title="数据库上传"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={900}
      className="database-upload-modal common-modal"
      closeIcon={<CloseOutlined />}
    >
      <Form form={form} layout="vertical" className="database-upload-form">
        <Form.Item label="数据源名称" name="name" rules={[{ required: true, message: '请输入数据源名称' }]}>
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item label="数据源描述" name="description">
          <TextArea placeholder="描述数据集的来源、作用,可不填写" rows={4} />
        </Form.Item>

        <Form.Item label="主机名 / IP地址" name="host" rules={[{ required: true, message: '请输入主机名/IP地址' }]}>
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item label="端口" name="port" rules={[{ required: true, message: '请输入端口' }]}>
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item label="数据库名称" name="dataBaseName" rules={[{ required: true, message: '请输入数据库名称' }]}>
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item label="用户名" name="userName" rules={[{ required: true, message: '请输入用户名' }]}>
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item label="密码" name="password" rules={[{ required: true, message: '请输入密码' }]}>
          <Input.Password placeholder="请输入名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DatabaseUploadModal;
