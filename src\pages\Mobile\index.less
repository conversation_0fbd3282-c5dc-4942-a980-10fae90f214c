.mobile-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #f8f9fa, #e0e0e0);
  padding: 16px;

  .content-card {
    background: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 24px;
    max-width: 90vw;
    text-align: center;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.6s ease;

    .title {
      font-size: 20px;
      color: #222;
      margin-bottom: 16px;
    }

    .paragraph {
      font-size: 16px;
      color: #555;
      line-height: 1.6;
      margin-bottom: 24px;
    }

    .back-btn {
      width: 100%;
      font-size: 16px;
      height: 40px;
      border-radius: 8px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
