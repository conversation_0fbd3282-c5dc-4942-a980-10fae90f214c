// 主弹窗样式
.data-source-modal {
  .form-item {
    margin-bottom: 24px;

    .ant-form-item-label {
      label {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
  }

  // 添加数据表按钮
  .data-table-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .add-table-button {
      width: 96px;
      height: 96px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px dashed #d9d9d9;
      border-radius: 4px;

      .anticon {
        font-size: 24px;
        color: #d9d9d9;
      }

      &:hover {
        border-color: #2868e7;

        .anticon {
          color: #2868e7;
        }
      }
    }
  }

  // 表单分隔线
  .form-section-divider {
    height: 1px;
    background-color: #e8e8e8;
    margin: 24px 0;
  }

  // 表单区域标题
  .form-section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
  }
}

// API连接弹窗样式
.api-connection-modal {
  // 步骤条
  .api-steps {
    margin-bottom: 24px;

    :global {
      .ant-steps-item-title {
        font-size: 14px;
        font-weight: 600;
      }

      .ant-steps-item-description {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }

      .ant-steps-item-icon {
        background: #fff;
        border-color: #2868e7;
      }

      .ant-steps-item-active .ant-steps-item-icon {
        background: #2868e7;
        border-color: #2868e7;
      }
    }
  }

  // 请求标签页
  .request-tabs {
    .ant-tabs-nav {
      margin-bottom: 16px;
    }
  }

  // 请求体类型
  .content-type-item {
    .ant-radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
  }

  // 超时输入框
  .timeout-input {
    width: 100%;
  }

  // 提取数据占位
  .extract-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 160px;
    color: #999;
  }
}
