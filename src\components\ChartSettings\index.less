.theme-config {
  width: 100%;
  font-size: 14px;

  .ant-collapse {
    background-color: transparent;

    .ant-collapse-item {
      border-bottom: none;

      .ant-collapse-header {
        padding: 12px 0;
        font-weight: 600;

        .ant-collapse-arrow {
          right: 16px;
          left: auto;
        }
      }

      .ant-collapse-content {
        border-top: none;

        .ant-collapse-content-box {
          padding: 0 0 16px;
        }
      }
    }
  }

  .config-panel {
    background-color: #fff;
    border-radius: 2px;
  }

  .config-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .label {
      width: 70px;
      margin-right: 8px;
      color: rgba(0, 0, 0, 0.85);
    }

    .sub-label {
      width: 70px;
      margin-right: 8px;
      color: rgba(0, 0, 0, 0.65);
    }

    .color-select {
      width: 100%;

      .ant-select-selector {
        padding: 0 8px !important;
      }
    }

    .data-select {
      flex: 1;
    }

    .slider-container {
      flex: 1;
      display: flex;
      align-items: center;

      .width-slider {
        flex: 1;
        margin-right: 12px;
      }

      .slider-value {
        width: 40px;
        text-align: right;
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .axis-input,
    .text-input,
    .grid-width-input {
      flex: 1;
    }

    .grid-style-select {
      width: 120px !important;
      margin-right: 8px;

      .ant-select-selector {
        padding: 0 8px !important;
      }

      .ant-select-selection-item {
        padding-right: 20px !important;
      }
    }

    .grid-width-input {
      width: 50px;
      margin-right: 8px;
    }

    .color-block {
      width: 24px;
      min-width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &.text-color,
      &.grid-color {
        margin-left: 12px;
      }
    }

    .unit-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;

      .ant-checkbox-wrapper {
        flex-shrink: 0;
        margin-bottom: 0;
      }

      .unit-input {
        flex: 1;
      }
    }
  }

  .color-option {
    display: flex;
    align-items: center;
    gap: 8px;

    .color-block {
      width: 24px;
      min-width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .options-container {
    display: flex;
    flex-direction: column;

    .ant-checkbox-wrapper {
      margin-left: 0;
      margin-bottom: 12px;
    }

    .sub-options {
      margin-left: 24px;
      margin-bottom: 12px;
    }
  }

  // 下拉选择器样式
  .ant-select-selector {
    display: flex;
    align-items: center;
    height: 32px !important;
    padding: 0 8px !important;

    .ant-select-selection-item {
      display: flex;
      align-items: center;
    }
  }

  // 线条样式预览
  .line-style-preview {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 4px 0;

    .line {
      width: 40px;
      height: 0;
      border-width: 1.5px 0 0 0;
      border-color: #333;
      margin: 0 8px;

      &.solid {
        border-style: solid;
      }

      &.dashed {
        border-style: dashed;
      }

      &.dotted {
        border-style: dotted;
      }
    }

    .text {
      min-width: 32px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.theme-config.vertical-layout {
  .options-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

.theme-config.horizontal-layout {
  display: flex;
  flex-direction: row;
  gap: 32px;

  .ant-collapse {
    flex: 1;
  }

  .options-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .horizontal-panels {
    width: 100%;
    display: flex;
    gap: 32px;

    .left-panel,
    .right-panel {
      flex: 1;
      min-width: 0;
    }
  }
}
