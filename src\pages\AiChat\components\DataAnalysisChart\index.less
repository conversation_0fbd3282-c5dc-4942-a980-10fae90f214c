.data-analysis-container {
  padding: 0;
  width: 100%;
  min-width: 650px;

  .header-wrapper {
    border-bottom: 1px solid #e8e8e8;

    .custom-tabs {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }

    .shared-apps {
      padding: 0 16px;
      font-size: 12px;
      color: #666;
    }
  }

  .content {
    margin-top: 16px;
    padding: 16px;
    background-color: #fff;
    border-radius: 16px;
    width: 100%;
    box-sizing: border-box; // 确保padding不会增加总宽度

    .title-row {
      display: flex;
      align-items: center;
      width: 100%;
      padding-bottom: 12px;
      border-bottom: 1px solid #d1d5db;
      .analysis-title {
        max-width: 85%;
        margin-bottom: 0;
        font-size: 18px;
        font-weight: 600;
        color: #2868e7;
        // 不能换行 省略号
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .button-right {
        margin-left: auto;
      }
    }

    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .chart-name {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        font-weight: 600;
        &:before {
          content: '';
          display: inline-block;
          width: 3px;
          height: 18px;
          background-color: #2868e7;
        }
      }

      .right-actions {
        display: flex;
        align-items: center;
      }
    }

    .chart-card {
      .ant-card-body {
        padding: 0;
      }
      .chart-container {
        width: 100%;
        height: 400px;
        display: block;
      }
    }

    .view-full {
      display: flex;
      justify-content: center;
      margin-top: 10px;
    }
    .analysis-card-container {
      margin-top: 12px;
      height: calc(100vh - 238px);
      background-image: url('../../../../../public/assets/42upswdn.svg');
      background-size: 99% 100%;
      background-repeat: no-repeat;
      overflow-y: auto;
      min-height: 300px; // 最小高度保护
      transition: height 0.2s ease-in-out; // 平滑过渡
    }

    .analysis-card-wrapper {
      padding: 12px 24px;
      overflow: hidden;
      .analysis-card-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        font-weight: 600;
        color: #2868e7;
        margin: 16px 0;
        img {
          width: 22px;
          height: 22px;
          object-fit: cover;
        }
      }
      .analysis-card-wrapper {
        background-color: #fff;
        padding: 16px;
        border-radius: 16px;
        margin-bottom: 16px;

        .paragraph-body {
          h3 {
            width: max-content;
            padding: 2px 8px;
            font-weight: 600;
            border-radius: 4px;
            font-size: 14px;
          }
          h3:nth-of-type(1) {
            border: 1px solid #50b3f5;
            background: rgba(142, 207, 251, 0.6);
          }
          h3:nth-of-type(2) {
            border: 1px solid #e86541;
            background: rgba(239, 151, 151, 0.6);
          }
          h3:nth-of-type(3) {
            border: 1px solid #1d4fd7;
            background: rgba(29, 79, 215, 0.6);
          }
          h3:nth-of-type(4) {
            border: 1px solid #e86541;
            background: rgba(239, 151, 151, 0.6);
          }
          h3:nth-of-type(5) {
            border: 1px solid #e86541;
            background: rgba(239, 151, 151, 0.6);
          }
        }
      }
    }
  }
}

.file-bar-wrapper {
  padding: 16px;
  margin: 16px 24px 0 24px; // 与 analysis-card-wrapper 的 padding 保持一致
  background-color: #fff;
  border-radius: 16px;

  .ant-card-body {
    padding: 0;
  }

  .analysis-title {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2868e7;
  }
}

.file-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 8px 0; // 减少上下边距，避免影响布局
  border: 1px solid #e8e8e8;

  .file-name {
    color: #3578e5;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 60%;
    transition: color 0.2s;

    &:hover {
      color: #1d4fd7;
    }
  }

  .file-action-bar {
    display: flex;
    align-items: center;
    gap: 16px;

    .file-action-btn {
      display: flex;
      align-items: center;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;
      white-space: nowrap;
      padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        color: #3578e5;
        background-color: rgba(53, 120, 229, 0.1);
      }

      .icon {
        margin-right: 6px;
        font-size: 16px;
        vertical-align: middle;
      }
    }

    .divider {
      width: 1px;
      height: 16px;
      background: #e5e6eb;
      margin: 0 4px;
    }
  }
}
