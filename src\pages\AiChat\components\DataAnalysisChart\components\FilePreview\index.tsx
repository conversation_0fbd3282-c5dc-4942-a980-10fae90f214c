import React, { useState, useEffect } from 'react';
import { message, Input, Button, Checkbox, Radio, Spin, Table, Tabs } from 'antd';
import * as XLSX from 'xlsx';
import <PERSON> from 'papaparse';
import Luckysheet from '@/components/Luckysheet';
import { downloadFile, getMysqlTableFields } from '@/services/DataLoom/fileController';
import { useModel } from '@umijs/max';
import { DataSourceItem } from '@/models/dataSource';
import './index.less';

interface TableSelectProps {
  onOk: (selectedData: any[][]) => void;
  onCancel: () => void;
}

const TableSelect: React.FC<TableSelectProps> = ({ onOk, onCancel }) => {
  const [searchText, setSearchText] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [luckysheetData, setLuckysheetData] = useState<Record<string, any[]>>({});
  const [selectedRange, setSelectedRange] = useState<Record<string, any>>({});
  const [selectedData, setSelectedData] = useState<Record<string, any>>({});
  const [viewMode, setViewMode] = useState<'all' | 'selected'>('all');
  const [tableFields, setTableFields] = useState<Record<string, API.CoreDatasetTableField[]>>({});
  const [activeTab, setActiveTab] = useState<string>('');
  const { selectedSources, addSource, removeSource } = useModel('dataSource');
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  // 处理标签页编辑事件
  const handleEdit = (targetKey: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => {
    if (action === 'remove' && typeof targetKey === 'string') {
      // 如果删除的是当前激活的标签页，需要切换到其他标签页
      if (targetKey === activeTab) {
        const newDataSource = selectedSources?.filter((source) => source.id !== targetKey);
        if (newDataSource && newDataSource.length > 0) {
          setActiveTab(newDataSource[0].id);
        } else {
          setActiveTab('');
        }
      }
      // 调用removeSource移除数据源
      removeSource(targetKey);
    }
  };

  // 定义表格列
  const columns = [
    {
      title: '字段名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '原始字段名',
      dataIndex: 'originName',
      key: 'originName',
    },
    {
      title: '字段类型',
      dataIndex: 'fieldType',
      key: 'fieldType',
    },
  ];

  useEffect(() => {
    if (!selectedSources || selectedSources.length === 0) {
      setActiveTab('');
      return;
    }

    // 如果当前激活的标签页不在数据源中，设置第一个标签页为激活状态
    if (!activeTab || !selectedSources.some((source) => source.id === activeTab)) {
      setActiveTab(selectedSources[0].id);
    }

    // 加载所有表格数据
    selectedSources.forEach((source) => {
      if (source.type === 'mysql') {
        const params = {
          datasourceId: source.datasourceId,
          tableName: source.table,
        };
        getMysqlTableFields(params)
          .then((res) => {
            if (res?.data) {
              setTableFields((prev) => ({
                ...prev,
                [source.id]: res.data,
              }));
            }
          })
          .catch((err) => {
            message.error(`获取表 ${source.table} 结构失败`);
          });
      } else {
        fetchCSVData(source);
      }
    });
  }, [selectedSources]);

  const fetchCSVData = async (source: DataSourceItem) => {
    if (!currentUser?.id) {
      message.error('用户信息不存在');
      return;
    }

    try {
      const response = await downloadFile({
        fileName: source.name,
        userId: currentUser.id.toString(),
      });

      const buffer = await response.arrayBuffer();
      const fileExtension = source.name.split('.').pop()?.toLowerCase();
      let raw: string[][] = [];

      if (fileExtension === 'csv') {
        const text = new TextDecoder('utf-8').decode(buffer);
        if (!text || text.trim().length === 0) {
          throw new Error('文件内容为空');
        }

        // 移除UTF-8 BOM标记
        const cleanText = text.charCodeAt(0) === 0xfeff ? text.slice(1) : text;

        const result = Papa.parse<string[]>(cleanText, {
          header: false,
          skipEmptyLines: true,
          delimiter: ',', // 明确指定分隔符为英文逗号
          transform: (value: string) => value.trim(),
        });

        if (result.errors.length > 0) {
          throw new Error('CSV文件格式错误');
        }

        raw = result.data as string[][];
      } else if (fileExtension === 'xls' || fileExtension === 'xlsx') {
        const workbook = XLSX.read(buffer, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        raw = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

        if (!raw || raw.length === 0) {
          throw new Error('Excel文件内容为空');
        }
      } else {
        throw new Error('不支持的文件格式');
      }

      setLuckysheetData((prev) => ({
        ...prev,
        [source.id]: raw,
      }));
    } catch (err) {
      message.error(`文件 ${source.name} 加载失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  // 添加检查区域是否全选的辅助函数
  const isFullRangeSelected = (range: any, tableId: string) => {
    if (!range || !luckysheetData[tableId]?.length) return false;

    const rowCount = luckysheetData[tableId].length;
    const colCount = luckysheetData[tableId][0]?.length || 0;

    return (
      range.row &&
      range.column &&
      range.row[0] === 0 &&
      range.row[1] === rowCount - 1 &&
      range.column[0] === 0 &&
      range.column[1] === colCount - 1
    );
  };

  const handleSelectAll = (checked: boolean, tableId: string) => {
    setSelectAll(checked);

    // @ts-ignore
    const luckysheet = window.luckysheet;
    if (!luckysheet) return;

    if (checked && luckysheetData[tableId]?.length > 0) {
      const rowCount = luckysheetData[tableId].length;
      const colCount = luckysheetData[tableId][0]?.length || 0;

      try {
        if (typeof luckysheet.setRangeSelect === 'function') {
          luckysheet.setRangeSelect({
            row: [0, rowCount - 1],
            column: [0, colCount - 1],
          });
        } else if (typeof luckysheet.setRangeShow === 'function') {
          luckysheet.setRangeShow({
            row: [0, rowCount - 1],
            column: [0, colCount - 1],
            rangeType: 'range',
          });
        }

        setSelectedRange((prev) => ({
          ...prev,
          [tableId]: {
            row: [0, rowCount - 1],
            column: [0, colCount - 1],
          },
        }));
      } catch (error) {
        console.error('选择所有单元格失败:', error);
      }
    } else {
      setSelectedRange((prev) => ({
        ...prev,
        [tableId]: null,
      }));

      try {
        if (typeof luckysheet.clearSelection === 'function') {
          luckysheet.clearSelection();
        } else if (typeof luckysheet.setRangeShow === 'function') {
          luckysheet.setRangeShow({
            row: [0, 0],
            column: [0, 0],
            rangeType: 'range',
            borderType: 'none',
          });
        }
      } catch (error) {
        console.error('清除选择失败:', error);
      }
    }
  };

  const handleRangeSelect = (range: any, tableId: string) => {
    setSelectedData((prev) => ({
      ...prev,
      [tableId]: range.data,
    }));
    setSelectAll(isFullRangeSelected(range, tableId));
  };

  const handleDelete = (tableId: string) => {
    setSelectedData((prev) => ({
      ...prev,
      [tableId]: [],
    }));
    setViewMode('all');
  };

  const handleOk = () => {
    const currentData = selectedData[activeTab];
    if (!currentData || currentData.length === 0) {
      message.warning('请先选择数据');
      return;
    }
    onOk(currentData);
  };

  return (
    <div className="table-select-container">
      <Tabs
        hideAdd
        type="editable-card"
        activeKey={activeTab}
        onChange={setActiveTab}
        onEdit={handleEdit}
        items={selectedSources?.map((source) => ({
          key: source.id,
          label: source.name,
          children: (
            <>
              {source.type !== 'mysql' && (
                <div className="search-area">
                  <Radio.Group value={viewMode} onChange={(e) => setViewMode(e.target.value)}>
                    <Radio.Button value="all">全部数据</Radio.Button>
                    {/* {selectedData[source.id]?.length > 0 && <Radio.Button value="selected">已选择的数据</Radio.Button>} */}
                  </Radio.Group>
                  {/* <div className="operation-area">
                    <Input.Search
                      placeholder="请输入搜索内容"
                      className="search-input"
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      allowClear
                    />
                    {viewMode === 'all' && (
                      <Checkbox checked={selectAll} onChange={(e) => handleSelectAll(e.target.checked, source.id)}>
                        全选
                      </Checkbox>
                    )}
                    {viewMode === 'selected' && (
                      <Button type="primary" danger onClick={() => handleDelete(source.id)}>
                        删除
                      </Button>
                    )}
                  </div> */}
                </div>
              )}
              {source.type === 'mysql' && (
                <div style={{ marginBottom: 16 }}>
                  <Table
                    columns={columns}
                    dataSource={tableFields[source.id] || []}
                    rowKey={(record) => record.id || record.name || Math.random().toString()}
                    pagination={false}
                    size="small"
                    bordered
                  />
                </div>
              )}
            </>
          ),
        }))}
      />
      {activeTab && selectedSources.find((source) => source.id === activeTab)?.type !== 'mysql' && (
        <div style={{ height: 'calc(100vh - 250px)', width: '100%' }}>
          <Luckysheet
            key={activeTab}
            data={viewMode === 'all' ? luckysheetData[activeTab] || [] : selectedData[activeTab] || []}
            onRangeSelect={(range) => handleRangeSelect(range, activeTab)}
          />
        </div>
      )}
    </div>
  );
};

export default TableSelect;
