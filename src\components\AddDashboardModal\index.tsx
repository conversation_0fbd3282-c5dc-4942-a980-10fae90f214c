import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { listUserDataSource } from '@/services/DataLoom/coreDataSourceController';
import { addDashboard } from '@/services/DataLoom/yibiaopanjiekou';
import TableSelectModal from '../TableSelectModal';
import './index.less';

interface AddDashboardModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const AddDashboardModal: React.FC<AddDashboardModalProps> = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [datasources, setDatasources] = useState<any[]>([]);
  const [tableSelectVisible, setTableSelectVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      loadDatasources();
    }
  }, [visible]);

  const loadDatasources = async () => {
    try {
      const res = await listUserDataSource();
      if (res?.code === 0 && res?.data?.records) {
        const datasourceOptions = res.data.records
          .map((item: any) => ({
            label: (
              <>
                {item.type === 'excel' && <img src="/assets/Excel.svg" />}
                {item.type === 'mysql' && <img src="/assets/Mysql.svg" />}
                {item.type === 'api' && <img src="/assets/API.svg" />}
                <span style={{ marginLeft: '4px' }}>{item.name || '未命名数据源'}</span>
              </>
            ),
            value: item.id,
          }))
          .filter((option: any) => option.value); // 过滤掉没有id的选项
        setDatasources(datasourceOptions);
      } else {
        console.warn('加载数据源失败:', res?.message || '未知错误');
        message.warning('加载数据源失败，请稍后重试');
        setDatasources([]);
      }
    } catch (error) {
      console.error('加载数据源异常:', error);
      message.error('加载数据源失败，请检查网络连接');
      setDatasources([]);
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const res = await addDashboard({ ...values, pid: 0 });
      if (res?.code === 0) {
        message.success('新建成功');
        form.resetFields();
        onSuccess();
      } else {
        message.error(res?.message || '创建失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('表单验证或提交失败:', error);
      if (error?.errorFields) {
        // 表单验证失败
        message.error('请检查表单填写是否正确');
      } else {
        // 网络或其他异常
        message.error('操作失败，请检查网络连接后重试');
      }
    }
  };

  const handleTableSelect = () => {
    try {
      const datasourceId = form.getFieldValue('datasourceId');
      if (!datasourceId) {
        message.warning('请先选择数据源');
        return;
      }
      setTableSelectVisible(true);
    } catch (error) {
      console.error('表格选择异常:', error);
      message.error('操作失败，请重试');
    }
  };

  return (
    <>
      <Modal
        title="添加仪表盘"
        open={visible}
        onOk={handleModalOk}
        onCancel={() => {
          form.resetFields();
          onCancel();
        }}
        width="40%"
        destroyOnHidden
        className="add-dashboard-modal common-modal"
      >
        <Form form={form} layout="vertical" initialValues={{ datasourceId: undefined, name: '' }}>
          <Form.Item name="datasourceId" label="数据源" rules={[{ required: true, message: '请选择数据源' }]}>
            <Select placeholder="请选择数据源" options={datasources} />
          </Form.Item>
          <div style={{ marginTop: -8, marginBottom: 16 }}>
            <a onClick={handleTableSelect}>勾选具体列表</a>
          </div>
          <Form.Item name="name" label="仪表盘名称" rules={[{ required: true, message: '请输入仪表盘名称' }]}>
            <Input placeholder="请输入仪表盘名称" />
          </Form.Item>
        </Form>
      </Modal>

      <TableSelectModal
        visible={tableSelectVisible}
        onCancel={() => setTableSelectVisible(false)}
        onOk={() => {
          setTableSelectVisible(false);
          // 这里可以处理选择的数据
        }}
      />
    </>
  );
};

export default AddDashboardModal;
