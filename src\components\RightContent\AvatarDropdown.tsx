import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { history, useModel } from '@umijs/max';
import { Badge, Spin } from 'antd';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback } from 'react';
import { flushSync } from 'react-dom';
import HeaderDropdown from '../HeaderDropdown';
import { userLogout } from '@/services/DataLoom/userController';
import defaultSettings from '../../../config/defaultSettings';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">{currentUser?.userName}</span>;
};

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    await userLogout();
    const { search, pathname } = window.location;
    const urlParams = new URL(window.location.href).searchParams;
    /** 此方法会跳转到 redirect 参数所在的位置 */
    const redirect = urlParams.get('redirect');
    // Note: There may be security issues, please note
    if (window.location.pathname !== '/user/login' && !redirect) {
      history.replace({
        pathname: '/user/login',
        search: stringify({
          redirect: pathname + search,
        }),
      });
    }
  };
  const actionClassName = useEmotionCss(({ token }) => {
    return {
      'display': 'flex',
      'height': '48px',
      'marginLeft': 'auto',
      'overflow': 'hidden',
      'alignItems': 'center',
      'padding': '0 8px',
      'cursor': 'pointer',
      'borderRadius': token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    };
  });
  const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        flushSync(() => {
          setInitialState((s) => ({ ...s, currentUser: undefined, settings: defaultSettings }));
        });
        loginOut();
        return;
      }
      if (key === 'profile') {
        // 传递当前页面路径作为来源参数
        const currentPath = history.location.pathname + history.location.search;
        history.push(`/user/profile?from=${encodeURIComponent(currentPath)}`);
        return;
      }
    },
    [setInitialState],
  );

  const unLogin = (
    <span className={actionClassName}>
      <a
        onClick={() => {
          history.push('/user/login');
        }}
      >
        登录
      </a>
    </span>
  );

  if (!initialState) {
    return unLogin;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.userName) {
    return unLogin;
  }

  const menuItems = [
    {
      key: 'profile',
      icon: <img src="/assets/image_1751969179776_mguuk6.svg" alt="profile" />,
      label: '账号信息',
    },
    {
      key: 'logout',
      icon: <img src="/assets/image_1751969180516_zps2ub.svg" alt="logout" />,
      label: '退出当前账号',
    },
  ];

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children}
    </HeaderDropdown>
  );
};
