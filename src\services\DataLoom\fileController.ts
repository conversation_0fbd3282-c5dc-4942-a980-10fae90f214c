// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';
/** 文件下载 GET /file/download */
export async function downloadFile(
  params: {
    fileName: string;
    userId?: string;
  },
  options?: { [key: string]: any },
) {
  return request<Blob>('/admin/file/download', {
    method: 'GET',
    params,
    responseType: 'blob',
    ...(options || {}),
  });
}
/** 删除文件 POST /admin/file/delete */
export async function deleteFile(params: { fileName: string; userId?: string }) {
  return request<any>('/admin/file/delete', {
    method: 'POST',
    params,
  });
}
/** 获取mysql表字段信息 GET /file/getMysqlTableFields */
export async function getMysqlTableFields(params: any) {
  return request<any>('/admin/coreDatasource/getTableFields', {
    method: 'POST',
    data: params,
  });
}
