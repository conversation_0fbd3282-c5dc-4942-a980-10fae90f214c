import React, { useState } from 'react';
import { Modal, Radio, Button, Space } from 'antd';
import './index.less';

interface ExportTableModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (format: string) => void;
  data?: any[];
}

const ExportTableModal: React.FC<ExportTableModalProps> = ({ visible, onCancel, onOk }) => {
  const [format, setFormat] = useState<string>('excel');

  const handleFormatChange = (e: any) => {
    setFormat(e.target.value);
  };

  const handleConfirm = () => {
    onOk(format);
  };

  return (
    <Modal title="导出表格" open={visible} onCancel={onCancel} onOk={handleConfirm} width={620} className="export-table-modal common-modal">
      <div className="modal-content">
        <div className="format-section">
          <span className="required-mark">*</span>
          <span className="format-label">表格格式</span>
          <Radio.Group onChange={handleFormatChange} value={format} className="format-options">
            <Radio value="excel">EXCEL</Radio>
            <Radio value="csv">CSV</Radio>
          </Radio.Group>
        </div>
      </div>
    </Modal>
  );
};

export default ExportTableModal;
