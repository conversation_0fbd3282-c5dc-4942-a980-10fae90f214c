<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5691_32385)">
<g filter="url(#filter0_f_5691_32385)">
<rect x="13.4941" y="34.3223" width="29.1378" height="4.99972" fill="#A755FF" fill-opacity="0.8"/>
</g>
<ellipse cx="28.0637" cy="38.3222" rx="4.22967" ry="0.999944" fill="url(#paint0_radial_5691_32385)" fill-opacity="0.7"/>
<rect x="5.99609" y="8" width="30.0777" height="21.9988" rx="6" fill="url(#paint1_linear_5691_32385)"/>
<foreignObject x="8.55469" y="12.3223" width="38.0781" height="29.998"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_5691_32385_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_5691_32385)" data-figma-bg-blur-radius="4">
<rect x="12.5547" y="16.3223" width="30.0777" height="21.9988" rx="6" fill="#D899FF" fill-opacity="0.4"/>
<rect x="12.8047" y="16.5723" width="29.5777" height="21.4988" rx="5.75" stroke="url(#paint2_linear_5691_32385)" stroke-opacity="0.7" stroke-width="0.5"/>
</g>
<g filter="url(#filter2_di_5691_32385)">
<path d="M18.1934 21.0508C18.1934 20.4985 18.6411 20.0508 19.1934 20.0508H35.9919C36.5442 20.0508 36.9919 20.4985 36.9919 21.0508V22.3364H18.1934V21.0508Z" fill="url(#paint3_linear_5691_32385)"/>
</g>
<g filter="url(#filter3_i_5691_32385)">
<path d="M18.1934 22.623H36.9919V33.6224C36.9919 34.1747 36.5442 34.6224 35.9919 34.6224H19.1934C18.6411 34.6224 18.1934 34.1747 18.1934 33.6224V22.623Z" fill="url(#paint4_linear_5691_32385)"/>
</g>
<g filter="url(#filter4_i_5691_32385)">
<rect x="28.9336" y="27.1914" width="6.17667" height="0.285698" rx="0.142849" fill="#F1E4F9"/>
</g>
<g filter="url(#filter5_i_5691_32385)">
<rect x="28.9336" y="27.7656" width="6.17667" height="0.285698" rx="0.142849" fill="#F1E4F9"/>
</g>
<g filter="url(#filter6_i_5691_32385)">
<rect x="28.9336" y="28.3379" width="6.17667" height="0.285698" rx="0.142849" fill="#F1E4F9"/>
</g>
<g filter="url(#filter7_i_5691_32385)">
<rect x="28.9336" y="28.9082" width="6.17667" height="0.285698" rx="0.142849" fill="#F1E4F9"/>
</g>
<g filter="url(#filter8_i_5691_32385)">
<rect x="28.9336" y="29.4785" width="6.17667" height="0.285698" rx="0.142849" fill="#F1E4F9"/>
</g>
<g filter="url(#filter9_dii_5691_32385)">
<ellipse cx="19.6724" cy="21.3348" rx="0.402826" ry="0.428548" fill="#8616D0"/>
</g>
<g filter="url(#filter10_dii_5691_32385)">
<ellipse cx="20.7446" cy="21.3348" rx="0.402826" ry="0.428548" fill="#8616D0"/>
</g>
<g filter="url(#filter11_dii_5691_32385)">
<ellipse cx="21.8169" cy="21.3348" rx="0.402826" ry="0.428548" fill="#8616D0"/>
</g>
<g filter="url(#filter12_dii_5691_32385)">
<rect x="33.2324" y="21.1934" width="2.95406" height="0.285698" rx="0.142849" fill="#8616D0"/>
</g>
<g filter="url(#filter13_dii_5691_32385)">
<path d="M27.0585 28.6232C27.0585 29.284 26.8627 29.928 26.4989 30.4637C26.1351 30.9994 25.6218 31.3994 25.032 31.6069C24.4423 31.8144 23.806 31.8188 23.2137 31.6196C22.6215 31.4203 22.1033 31.0275 21.7329 30.497C21.3625 29.9664 21.1588 29.3252 21.1506 28.6644C21.1425 28.0036 21.3304 27.3569 21.6875 26.8162C22.0447 26.2755 22.553 25.8684 23.1401 25.6526C23.7273 25.4369 24.3634 25.4236 24.9581 25.6146L24.1044 28.6232H27.0585Z" fill="#8616D0"/>
</g>
<g filter="url(#filter14_dii_5691_32385)">
<path d="M27.5937 28.336C27.5937 27.6605 27.3891 27.003 27.0102 26.4609C26.6314 25.9189 26.0984 25.5211 25.4904 25.3265L24.6396 28.336H27.5937Z" fill="#8616D0"/>
</g>
</g>
<defs>
<filter id="filter0_f_5691_32385" x="3.09414" y="23.9223" width="49.9387" height="25.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.2" result="effect1_foregroundBlur_5691_32385"/>
</filter>
<filter id="filter1_i_5691_32385" x="8.55469" y="12.3223" width="38.0781" height="29.998" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.656237 0 0 0 0 0.0581084 0 0 0 0 0.805769 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<clipPath id="bgblur_1_5691_32385_clip_path" transform="translate(-8.55469 -12.3223)"><rect x="12.5547" y="16.3223" width="30.0777" height="21.9988" rx="6"/>
</clipPath><filter id="filter2_di_5691_32385" x="17.1934" y="20.0508" width="20.7988" height="4.28516" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.504369 0 0 0 0 0.0748799 0 0 0 0 0.855769 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.822767 0 0 0 0 0.627369 0 0 0 0 0.944231 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
</filter>
<filter id="filter3_i_5691_32385" x="18.1934" y="21.623" width="18.7988" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.824827 0 0 0 0 0.711795 0 0 0 0 0.917308 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<filter id="filter4_i_5691_32385" x="28.9336" y="27.1914" width="6.37578" height="0.485156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.984121 0 0 0 0 0.960577 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<filter id="filter5_i_5691_32385" x="28.9336" y="27.7656" width="6.37578" height="0.485156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.984121 0 0 0 0 0.960577 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<filter id="filter6_i_5691_32385" x="28.9336" y="28.3379" width="6.37578" height="0.485156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.984121 0 0 0 0 0.960577 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<filter id="filter7_i_5691_32385" x="28.9336" y="28.9082" width="6.37578" height="0.485156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.984121 0 0 0 0 0.960577 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<filter id="filter8_i_5691_32385" x="28.9336" y="29.4785" width="6.37578" height="0.485156" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.984121 0 0 0 0 0.960577 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_32385"/>
</filter>
<filter id="filter9_dii_5691_32385" x="18.4695" y="20.3063" width="2.80469" height="2.85742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.4"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.214458 0 0 0 0 0.084828 0 0 0 0 0.240385 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.821461 0 0 0 0 0.556731 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5691_32385" result="effect3_innerShadow_5691_32385"/>
</filter>
<filter id="filter10_dii_5691_32385" x="19.5418" y="20.3063" width="2.80469" height="2.85742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.4"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.214458 0 0 0 0 0.084828 0 0 0 0 0.240385 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.821461 0 0 0 0 0.556731 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5691_32385" result="effect3_innerShadow_5691_32385"/>
</filter>
<filter id="filter11_dii_5691_32385" x="20.6141" y="20.3063" width="2.80469" height="2.85742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.4"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.214458 0 0 0 0 0.084828 0 0 0 0 0.240385 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.821461 0 0 0 0 0.556731 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5691_32385" result="effect3_innerShadow_5691_32385"/>
</filter>
<filter id="filter12_dii_5691_32385" x="32.4324" y="20.5934" width="4.95312" height="2.28516" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.4"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.214458 0 0 0 0 0.084828 0 0 0 0 0.240385 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.821461 0 0 0 0 0.556731 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5691_32385" result="effect3_innerShadow_5691_32385"/>
</filter>
<filter id="filter13_dii_5691_32385" x="20.3504" y="24.8805" width="7.9082" height="8.28516" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.4"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.214458 0 0 0 0 0.084828 0 0 0 0 0.240385 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.821461 0 0 0 0 0.556731 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5691_32385" result="effect3_innerShadow_5691_32385"/>
</filter>
<filter id="filter14_dii_5691_32385" x="23.8387" y="24.7262" width="4.95508" height="5.00977" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.4"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.214458 0 0 0 0 0.084828 0 0 0 0 0.240385 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_32385"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_32385" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.2" dy="0.2"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.821461 0 0 0 0 0.556731 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_32385"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5691_32385" result="effect3_innerShadow_5691_32385"/>
</filter>
<radialGradient id="paint0_radial_5691_32385" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(28.0637 38.3222) scale(4.22967 0.999944)">
<stop offset="0.508354" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_5691_32385" x1="21.0349" y1="8" x2="21.0349" y2="29.9988" gradientUnits="userSpaceOnUse">
<stop stop-color="#8012C9"/>
<stop offset="1" stop-color="#AE30FF"/>
</linearGradient>
<linearGradient id="paint2_linear_5691_32385" x1="13.8915" y1="19.2554" x2="32.5921" y2="42.5292" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9BAFF"/>
<stop offset="1" stop-color="#F36BFF"/>
</linearGradient>
<linearGradient id="paint3_linear_5691_32385" x1="27.5926" y1="20.0508" x2="27.5926" y2="22.3364" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FBF2FF"/>
</linearGradient>
<linearGradient id="paint4_linear_5691_32385" x1="27.5926" y1="22.623" x2="27.5926" y2="34.6224" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F8F2FF"/>
</linearGradient>
<clipPath id="clip0_5691_32385">
<rect width="48" height="48" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
