.profile-page-bg {
  height: 100%;
  padding: 16px 24px;
}

.full-screen-card {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  background: url('/assets/image_1751970653406_6n43q4.png') no-repeat center center;
  background-size: cover;
  .ant-card-body {
    padding: 100px;
  }
}

.profile-title {
  position: absolute;
  top: 24px;
  left: 24px;
  font-size: 12px;
  display: flex;
  align-items: center;
  span {
    line-height: 1;
  }
  .profile-title-text {
    color: #2868e7;
  }
  .profile-title-back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-left: 10px;
    cursor: pointer;
  }
}

.profile-container {
  width: 420px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.profile-row {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}

.profile-label {
  min-width: 120px;
  color: #6b7280;
  font-size: 14px;
  margin-right: 24px;
  text-align: left;
}

.profile-value {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
