import React, { useState, useRef, useEffect } from 'react';
import { Layout, Input, Button, Card, Tabs, List, Typography, Space, Divider, Badge, message as antMessage, Dropdown, Modal } from 'antd';
import {
  SearchOutlined,
  MoreOutlined,
  ArrowUpOutlined,
  BulbOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  Ellip<PERSON>Outlined,
} from '@ant-design/icons';
import ChatSidebar from './components/ChatSidebar';
import DataAnalysisChart from './components/DataAnalysisChart';
import { getIconBgColors, getImageByType, hasCustomImage } from '@/config/analyst';
import { getAiAssistantList } from '@/services/DataLoom/aiRoleController';
import './index.less';
import { getUserChatRecords, updateChatTitle, deleteChatHistory } from '@/services/DataLoom/aiController';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useModel } from 'umi';
import { debounce } from 'lodash';
import HotAssistantBar from './components/HotAssistantBar';

const { Content } = Layout;
const { TabPane } = Tabs;
const { Paragraph } = Typography;

const AnalyticsDashboard: React.FC = () => {
  const [searchText, setSearchText] = useState<string>('');
  const searchTimer = useRef<NodeJS.Timeout>();
  const [chatInput, setChatInput] = useState<string>('');
  const [showChart, setShowChart] = useState<boolean>(false);
  const [historyAnalyst, setHistoryAnalyst] = useState<any[]>([]);
  const [currentHistory, setCurrentHistory] = useState<any>(null);
  const [currentAnalyst, setCurrentAnalyst] = useState<any>(null);
  const [activeTabKey, setActiveTabKey] = useState<string>('1');
  const [dataAnalysis, setDataAnalysis] = useState<any>(null);
  const [dataAnalysisActiveTab, setDataAnalysisActiveTab] = useState<string>('filePreview');

  const [dataSource, setDataSource] = useState<any>(null);
  const { selectedSources, addSource } = useModel('dataSource');
  const { chatHistory, clearHistory } = useModel('chatHistory');
  const [aiAssistantList, setAiAssistantList] = useState<any[]>([]);

  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);

  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false);

  // 统一的获取聊天记录函数
  const fetchChatRecords = async (params: { current: number; pageSize: number; title: string }) => {
    const { current, pageSize, title } = params;
    try {
      const res = await getUserChatRecords({
        current,
        pageSize,
        title,
      });
      const { code, data } = res;
      if (code === 0 && data && Array.isArray(data.records)) {
        if (current === 1) {
          setHistoryAnalyst(data.records);
        } else {
          setHistoryAnalyst([...historyAnalyst, ...data.records]);
        }
        setCurrent(current);
        const totalLoaded = current === 1 ? data.records.length : historyAnalyst.length + data.records.length;
        setHasMore(totalLoaded < data.total);
      } else {
        if (current === 1) {
          setHistoryAnalyst([]);
        }
        setHasMore(false);
      }
      return res;
    } catch (error) {
      antMessage.error('获取聊天记录失败');
      setHasMore(false);
      return null;
    }
  };

  // 使用 lodash 的 debounce 创建防抖搜索函数
  const debouncedSearch = useRef(
    debounce((value: string) => {
      fetchChatRecords({
        current: 1,
        pageSize: pageSize,
        title: value,
      });
    }, 500),
  ).current;

  // 处理历史对话点击
  const handleHistoryChatClick = (history: any) => {
    clearHistory();
    setCurrentHistory(history);
    setCurrentAnalyst(null);
    setShowChart(false);
  };

  // 处理卡片点击
  const handleCardClick = (analyst: any) => {
    setCurrentAnalyst(analyst);
    setShowChart(false);
  };

  // 处理重命名
  const handleRename = (analyst: any) => {
    Modal.confirm({
      title: '重命名',
      content: <Input defaultValue={analyst.title} placeholder="请输入新名称" />,
      okText: '确认',
      cancelText: '取消',
      onOk: (close) => {
        const input = document.querySelector('.ant-modal-content input') as HTMLInputElement;
        updateChatTitle({
          chatId: analyst.id,
          newTitle: input.value,
        }).then((res) => {
          antMessage.success('重命名成功');
          setHistoryAnalyst(historyAnalyst.map((item) => (item.id === analyst.id ? { ...item, title: input.value } : item)));
          close();
        });
      },
    });
  };

  // 处理删除
  const handleDelete = (analyst: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除 "${analyst.title}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteChatHistory({
          chatId: analyst.id,
        }).then((res) => {
          antMessage.success('删除成功');
          // 重新获取第一页数据
          fetchChatRecords({
            current: 1,
            pageSize: pageSize,
            title: searchText,
          });
        });
      },
    });
  };

  // 处理搜索文本变化
  const handleSearchChange = (value: string) => {
    setSearchText(value);
    debouncedSearch(value);
  };

  // 组件卸载时取消未执行的防抖函数
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // 处理加载更多
  const loadMoreData = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    try {
      await fetchChatRecords({
        current: current + 1,
        pageSize: pageSize,
        title: searchText,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getAiAssistantList().then((res) => {
      const { code, data } = res;
      if (code === 0 && Array.isArray(data)) {
        setAiAssistantList(data);
      } else {
        setAiAssistantList([]);
      }
    });
  }, []);
  useEffect(() => {
    // 检查是否有选中的数据源
    const hasSelectedSources = Array.isArray(selectedSources) && selectedSources.length > 0;

    // 检查 chatHistory 中是否有 assistant 消息包含 chartOption 或 uploadedFiles
    const hasSpecialContent = chatHistory?.some(
      (msg) =>
        msg.role === 'assistant' &&
        ((msg.echart_option?.[0] && Object.keys(msg.echart_option[0]).length > 0) || (msg.uploaded_files && msg.uploaded_files.length > 0)),
    );

    // 设置图表显示状态
    setShowChart(hasSelectedSources || hasSpecialContent);

    // 处理数据源
    if (hasSpecialContent) {
      // 如果有特殊内容，优先显示分析结果
      setDataAnalysisActiveTab('analysisResult');
    } else if (hasSelectedSources) {
      // 如果没有特殊内容但有选中的数据源，显示文件预览
      setDataAnalysisActiveTab('filePreview');
    }
  }, [selectedSources, chatHistory]);

  // 初始加载数据
  useEffect(() => {
    fetchChatRecords({
      current: 1,
      pageSize: pageSize,
      title: searchText,
    }).then((res) => {
      if (res?.code === 0 && res?.data?.records?.length > 0) {
        handleHistoryChatClick(res.data.records[0]);
      }
    });
  }, [pageSize]);

  const tabItems = [
    {
      key: '1',
      label: '热门推荐',
    },
  ];

  // 处理侧边栏收缩
  const handleSidebarCollapse = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className={`analytics-dashboard ${showChart ? 'analytics-dashboard-bg' : ''}`}>
      {/* 左侧边栏 */}
      <div className={`sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
        {/* 收缩和展开 */}
        <div className="sidebar-header">
          <span>历史对话</span>
          <img
            src={isSidebarCollapsed ? '/assets/t5hbz8e9.svg' : '/assets/r2l6w3pe.svg'}
            alt="collapse"
            className="collapse-icon"
            onClick={handleSidebarCollapse}
          />
        </div>
        <div className="search-container">
          {historyAnalyst.length > 0 && (
            <>
              {!isSidebarCollapsed && (
                <Input
                  placeholder="搜索历史对话"
                  value={searchText}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  suffix={<SearchOutlined className="search-icon" />}
                  className="search-input"
                />
              )}
              {isSidebarCollapsed && (
                <img
                  src="/assets/jbq9a3gu.svg"
                  alt="search"
                  className="search-icon-collapsed"
                  onClick={() => setIsSidebarCollapsed(false)}
                />
              )}
            </>
          )}
        </div>
        <div className="sidebar-analysts" id="sidebar-analysts">
          <InfiniteScroll
            dataLength={historyAnalyst.length}
            next={loadMoreData}
            hasMore={hasMore}
            loader={<div className="loading-more">加载中...</div>}
            scrollableTarget="sidebar-analysts"
            endMessage={historyAnalyst.length > 0 ? <div className="no-more-data">没有更多数据了</div> : null}
          >
            <List
              dataSource={historyAnalyst}
              loading={loading}
              renderItem={(history) => (
                <List.Item>
                  <div
                    className={`sidebar-analyst-item ${currentHistory?.id === history.id ? 'selected' : ''}`}
                    onClick={() => {
                      handleHistoryChatClick(history);
                    }}
                  >
                    <span className="analyst-title" title={history.title}>
                      {history.title}
                    </span>
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: 'rename',
                            icon: <EditOutlined />,
                            label: '重命名',
                            onClick: () => {
                              handleRename(history);
                            },
                          },
                          {
                            key: 'delete',
                            icon: <DeleteOutlined />,
                            label: '删除',
                            danger: true,
                            onClick: () => {
                              handleDelete(history);
                            },
                          },
                        ],
                      }}
                      trigger={['click']}
                    >
                      <Button type="text" icon={<MoreOutlined />} className="more-button" onClick={(e) => e.stopPropagation()} />
                    </Dropdown>
                  </div>
                </List.Item>
              )}
            />
          </InfiniteScroll>
        </div>
      </div>

      {/* 主内容区 */}
      <div className="main-content">
        {!showChart ? (
          <>
            {/* 顶部横幅 */}
            <div className="banner">
              <div className="banner-content">
                <div className="banner-title">
                  <span className="title-prefix">
                    <img src="/assets/mp7g7a7h.svg" alt="" />
                  </span>
                  <span className="title-suffix">智能分析师 决策随芯掌控</span>
                </div>
                <div className="banner-description">定制化数据分析模型，让每一个选择都准确如鲜</div>
                {/* <Input
                  className="banner-search-input"
                  placeholder="输入关键词查找您所需要的助手"
                  style={{ marginTop: 16, width: 320 }}
                  suffix={<SearchOutlined />}
                /> */}
              </div>
            </div>

            {/* 标签页 */}
            <div className="tabs-container">
              <Tabs defaultActiveKey="1" className="tabs-wrapper" activeKey={activeTabKey} items={tabItems} />
              <div className="usage-count">共{aiAssistantList.length}个应用</div>
            </div>

            {/* 分析师卡片网格 */}
            <div className="analyst-grid">
              {aiAssistantList.map((item, idx) => (
                <div key={item.id} className={`analyst-card ${currentAnalyst?.id === item.id ? 'selected' : ''}`}>
                  <div className="card-header">
                    <div
                      className="card-icon"
                      {...(!hasCustomImage(item.type) && { style: { backgroundColor: getIconBgColors(aiAssistantList.length)[idx] } })}
                    >
                      <img src={getImageByType(item.type || 'default')} alt={item.assistantName} />
                    </div>
                    <div className="card-title">{item.assistantName}</div>
                  </div>
                  <div className="card-content">
                    <div className="card-description">{item.functionDes}</div>
                    <Button type="primary" className=" chat-button" onClick={() => handleCardClick(item)}>
                      立即聊天
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <DataAnalysisChart
            onBack={() => setShowChart(false)}
            analyst={currentAnalyst}
            activeTab={dataAnalysisActiveTab}
            onTabChange={setDataAnalysisActiveTab}
          />
        )}
      </div>

      {/* 右侧边栏 - 新版本 */}
      <div className="right-sidebar-container">
        <ChatSidebar
          currentAnalyst={currentAnalyst}
          currentHistoryId={currentHistory?.id}
          onStartConversation={() => {
            setShowChart(false);
            setCurrentHistory(null);
            setCurrentAnalyst(null);
          }}
          onAnalystChange={(analyst) => {
            setCurrentAnalyst(analyst);
          }}
        />
        {showChart && (
          <HotAssistantBar
            aiAssistantList={aiAssistantList}
            currentAnalyst={currentAnalyst}
            onAnalystChange={(item) => {
              setCurrentAnalyst(item);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
