<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5691_26376)">
<path d="M18.6371 27.5642C19.2875 28.4604 20.6234 28.4604 21.2738 27.5642L33.5974 10.5853C34.379 9.50833 33.6097 7.9993 32.279 7.9993H7.63196C6.30127 7.9993 5.53192 9.50833 6.31356 10.5853L18.6371 27.5642Z" fill="url(#paint0_linear_5691_26376)"/>
<g filter="url(#filter0_f_5691_26376)">
<rect x="18.3281" y="36.7129" width="17.9196" height="3.25811" fill="#FFB555" fill-opacity="0.8"/>
</g>
<ellipse cx="26.8801" cy="39.1563" rx="2.85084" ry="0.814526" fill="url(#paint1_radial_5691_26376)" fill-opacity="0.7"/>
<foreignObject x="9.44141" y="7.46094" width="35.6934" height="35.6934"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_5691_26376_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_5691_26376)" data-figma-bg-blur-radius="4">
<ellipse cx="27.2884" cy="25.3079" rx="13.8469" ry="13.8469" fill="#FFC099" fill-opacity="0.4"/>
<path d="M27.2881 11.7109C34.7974 11.7109 40.8856 17.7984 40.8857 25.3076C40.8857 32.817 34.7975 38.9053 27.2881 38.9053C19.7788 38.9051 13.6914 32.8169 13.6914 25.3076C13.6916 17.7984 19.7789 11.7111 27.2881 11.7109Z" stroke="url(#paint2_linear_5691_26376)" stroke-opacity="0.7" stroke-width="0.5"/>
</g>
<g filter="url(#filter2_di_5691_26376)">
<path d="M21.6369 19.6055H33.753C34.2312 19.6055 34.6184 20.0046 34.6184 20.4964V23.1001C34.6184 23.5924 34.2307 23.9915 33.753 23.9915H21.6369C21.4074 23.9915 21.1873 23.8976 21.025 23.7306C20.8627 23.5635 20.7715 23.3369 20.7715 23.1006V20.4964C20.7715 20.0041 21.1592 19.6055 21.6369 19.6055ZM22.9284 21.3597C22.8154 21.3597 22.707 21.4059 22.6271 21.4881C22.5472 21.5704 22.5024 21.6819 22.5024 21.7982C22.5024 21.9146 22.5472 22.0261 22.6271 22.1083C22.707 22.1906 22.8154 22.2368 22.9284 22.2368H25.5381C25.6511 22.2368 25.7594 22.1906 25.8393 22.1083C25.9192 22.0261 25.9641 21.9146 25.9641 21.7982C25.9641 21.6819 25.9192 21.5704 25.8393 21.4881C25.7594 21.4059 25.6511 21.3597 25.5381 21.3597H22.9284ZM21.6369 26.6233H33.753C34.2312 26.6233 34.6184 27.0224 34.6184 27.5142V30.1184C34.6184 30.6102 34.2307 31.0088 33.753 31.0088H21.6369C21.4074 31.0088 21.1873 30.915 21.025 30.7479C20.8627 30.5808 20.7715 30.3542 20.7715 30.1179V27.5142C20.7715 27.0219 21.1592 26.6228 21.6369 26.6228V26.6233ZM22.9284 28.378C22.8154 28.378 22.707 28.4242 22.6271 28.5064C22.5472 28.5887 22.5024 28.7002 22.5024 28.8165C22.5024 28.9329 22.5472 29.0444 22.6271 29.1266C22.707 29.2089 22.8154 29.2551 22.9284 29.2551H25.5381C25.6511 29.2551 25.7594 29.2089 25.8393 29.1266C25.9192 29.0444 25.9641 28.9329 25.9641 28.8165C25.9641 28.7002 25.9192 28.5887 25.8393 28.5064C25.7594 28.4242 25.6511 28.378 25.5381 28.378H22.9284Z" fill="url(#paint3_linear_5691_26376)"/>
</g>
</g>
<defs>
<filter id="filter0_f_5691_26376" x="7.92813" y="26.3129" width="38.7199" height="24.0578" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.2" result="effect1_foregroundBlur_5691_26376"/>
</filter>
<filter id="filter1_i_5691_26376" x="9.44141" y="7.46094" width="35.6934" height="35.6934" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.805769 0 0 0 0 0.31979 0 0 0 0 0.0581084 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_26376"/>
</filter>
<clipPath id="bgblur_1_5691_26376_clip_path" transform="translate(-9.44141 -7.46094)"><ellipse cx="27.2884" cy="25.3079" rx="13.8469" ry="13.8469"/>
</clipPath><filter id="filter2_di_5691_26376" x="19.7715" y="19.6055" width="15.8477" height="13.4043" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.855769 0 0 0 0 0.348191 0 0 0 0 0.0748799 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_26376"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_26376" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.986538 0 0 0 0 0.828597 0 0 0 0 0.670656 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_26376"/>
</filter>
<linearGradient id="paint0_linear_5691_26376" x1="19.9551" y1="29.3802" x2="19.9551" y2="0.871743" gradientUnits="userSpaceOnUse">
<stop stop-color="#FC6E07"/>
<stop offset="1" stop-color="#FF8F38"/>
</linearGradient>
<radialGradient id="paint1_radial_5691_26376" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(26.8801 39.1563) scale(2.85084 0.814526)">
<stop offset="0.508354" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint2_linear_5691_26376" x1="14.6722" y1="15.1535" x2="38.6736" y2="37.0009" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEEBA"/>
<stop offset="1" stop-color="#FFC16B"/>
</linearGradient>
<linearGradient id="paint3_linear_5691_26376" x1="27.695" y1="19.6055" x2="27.695" y2="31.0088" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFF7F2"/>
</linearGradient>
<clipPath id="clip0_5691_26376">
<rect width="48" height="48" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
