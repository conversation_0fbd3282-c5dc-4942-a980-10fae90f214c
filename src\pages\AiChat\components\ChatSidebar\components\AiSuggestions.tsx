import React, { useEffect, useRef } from 'react';
import { <PERSON>ton, Collapse } from 'antd';
import { debounce } from 'lodash';
import { aiSuggest } from '@/services/DataLoom/yibiaopanjiekou';
import { DEFAULT_ANALYST_TYPE, DATA_TYPE_MAP } from '../utils';

// AI建议问题的类型定义
interface AiSuggestion {
  question: string;
}

interface DataSourceItem {
  id: string;
  name: string;
  type: string;
  table?: string;
  datasourceId: string;
}

interface AiSuggestionsProps {
  selectedAnalyst?: any;
  chatId: string;
  selectedSources: DataSourceItem[];
  currentUser?: any;
  aiSuggestions: (string | AiSuggestion)[];
  onSuggestionClick: (question: string) => void;
  onSuggestionsUpdate: (suggestions: (string | AiSuggestion)[]) => void;
}

const AiSuggestions: React.FC<AiSuggestionsProps> = ({
  selectedAnalyst,
  chatId,
  selectedSources,
  currentUser,
  aiSuggestions,
  onSuggestionClick,
  onSuggestionsUpdate,
}) => {
  // 使用 useRef 来保存防抖函数，避免重复创建
  const debouncedFetchAiSuggest = useRef(
    debounce(async (params: any) => {
      const res = await aiSuggest(params);
      const { code, data } = res;
      if (code === 0) {
        onSuggestionsUpdate(data?.data?.questions || []);
      }
    }, 300),
  ).current;

  // 猜你想要
  useEffect(() => {
    const params = {
      userId: currentUser?.id?.toString() || '',
      agentName: selectedAnalyst?.type || DEFAULT_ANALYST_TYPE,
      chatId: chatId,
      dataType: selectedSources.length ? DATA_TYPE_MAP[selectedSources[0].type] || 0 : 0,
      dataSource: {
        fileName: selectedSources.filter((source) => source.type === 'excel').map((source) => source.name),
        tableSet: selectedSources.filter((source) => source.type === 'mysql').map((source) => source.table),
        dataSourceId: selectedSources.length ? (selectedSources[0].type === 'mysql' ? selectedSources[0].datasourceId : '') : '',
      },
    };

    debouncedFetchAiSuggest(params);

    // 清理函数
    return () => {
      debouncedFetchAiSuggest.cancel();
    };
  }, [chatId, selectedSources, selectedAnalyst?.type, currentUser?.id, debouncedFetchAiSuggest, onSuggestionsUpdate]);

  if (!selectedAnalyst) {
    return null;
  }

  return (
    <Collapse
      className="guess-you-want"
      ghost
      defaultActiveKey={['1']}
      items={[
        {
          key: '1',
          label: '猜你想要',
          children: (
            <div className="guess-options">
              {aiSuggestions.map((item, index) => {
                // 处理可能是对象或字符串的情况
                const questionText = typeof item === 'object' && item !== null ? item.question : item;
                return (
                  <Button key={index} className="question-btn" onClick={() => onSuggestionClick(questionText)}>
                    <span title={questionText}>{questionText}</span>
                  </Button>
                );
              })}
            </div>
          ),
        },
      ]}
    />
  );
};

export default AiSuggestions;
