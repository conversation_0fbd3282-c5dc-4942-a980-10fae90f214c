{"name": "ant-design-pro", "version": "6.0.0", "private": true, "type": "module", "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "buildFab": "cross-env REACT_APP_ENV=fab max build", "build": "cross-env REACT_APP_ENV=prod max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "server": "npm run start:test", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env REACT_APP_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_DEV_SERVER_COMPRESS=none max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.8.0", "@ant-design/pro-card": "^2.5.27", "@ant-design/pro-components": "^2.3.57", "@ant-design/pro-form": "^2.23.1", "@ant-design/use-emotion-css": "1.0.4", "@types/react-resizable": "^3.0.8", "@umijs/route-utils": "^2.2.2", "antd": "^5.2.2", "antd-img-crop": "^4.13.0", "classnames": "^2.3.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "echart": "^0.1.3", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "framer-motion": "^12.16.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "node-fetch": "^3.3.2", "omit.js": "^2.0.2", "papaparse": "^5.5.2", "rc-menu": "^9.8.2", "rc-util": "^5.27.2", "react": "^18.2.0", "react-dev-inspector": "^1.8.4", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.4", "react-helmet-async": "^1.3.0", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.6", "react-intersection-observer": "^9.16.0", "react-markdown": "^8.0.7", "react-resizable": "^3.0.5", "react-spreadsheet": "^0.10.1", "react-syntax-highlighter": "^15.5.0", "rehype-raw": "^6.1.1", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.17", "@types/history": "^4.7.11", "@types/jest": "^29.4.0", "@types/lodash": "^4.14.191", "@types/papaparse": "^5.3.16", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-helmet": "^6.1.6", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.0.52", "@umijs/max": "^4.0.52", "add": "^2.0.6", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-plugin-unicorn": "^48.0.1", "express": "^4.21.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.4.3", "jest-environment-jsdom": "^29.4.3", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^3.0.3", "swagger-ui-dist": "^4.15.5", "ts-node": "^10.9.1", "typescript": "^4.9.5", "umi-presets-pro": "^2.0.2", "yarn": "^1.22.19"}, "engines": {"node": ">=12.0.0"}}