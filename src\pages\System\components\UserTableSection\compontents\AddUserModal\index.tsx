import React, { useState } from 'react';
import { Modal, Form, Input, Select, Switch, Checkbox, Button, Row, Col, Progress, Space, message } from 'antd';
import { set } from 'lodash';
import { addUser } from '@/services/DataLoom/userController';

const { Option } = Select;

interface AddUserModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
}

const departmentOptions = [
  { label: '影像科', value: '影像科' },
  { label: '内科', value: '内科' },
  { label: '外科', value: '外科' },
  // 可根据实际情况补充
];

const AddUserModal: React.FC<AddUserModalProps> = ({ visible, onOk, onCancel }) => {
  const [form] = Form.useForm();
  const [password, setPassword] = useState('');
  const [enabled, setEnabled] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(false);

  // 密码强度计算
  const getPasswordStrength = (pwd: string) => {
    if (!pwd) return 0;
    let score = 0;
    if (pwd.length >= 6) score += 1;
    if (/[A-Z]/.test(pwd) && /[a-z]/.test(pwd)) score += 1;
    if (/[0-9]/.test(pwd) && /[^A-Za-z0-9]/.test(pwd)) score += 1;
    return score;
  };
  const strength = getPasswordStrength(password);
  const strengthMap = ['弱', '中', '强'];

  // 新增用户逻辑
  const handleOk = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const requestData = {
        userAccount: values.userAccount,
        userName: values.userName,
        userPassword: values.userPassword,
        userAvatar: values.userAvatar || '',
        userRole: isAdmin ? 'admin' : 'user',
        position: values.position,
        department: values.department,
        tel: values.tel,
      };
      const response = await addUser(requestData);
      if (response.code === 0) {
        message.success('新建账号成功');
        form.resetFields();
        setPassword('');
        setEnabled(true);
        setIsAdmin(false);
        onCancel(); // 关闭弹窗
        onOk();
      } else {
        message.error(response.message || '新建账号失败');
      }
    } catch (error: any) {
      // 校验失败或接口异常
      if (error && error.errorFields) return; // 表单校验失败
      message.error('新建账号失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setPassword('');
    setEnabled(true);
    setIsAdmin(false);
    onCancel();
  };

  return (
    <Modal
      className="add-user-modal common-modal"
      title="新建账号"
      open={visible}
      width={750}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnHidden={true}
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label="账号名称"
          name="userAccount"
          rules={[
            { required: true, message: '请输入账号名称' },
            { min: 6, message: '账号名称不得少于6位' },
          ]}
        >
          <Input placeholder="请输入内容" />
        </Form.Item>
        <Form.Item label="使用科室" name="department" rules={[{ required: true, message: '请选择使用科室' }]}>
          <Select placeholder="请选择科室">
            {departmentOptions.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label="使用人" name="userName" rules={[{ required: true, message: '请输入使用人' }]}>
          <Input placeholder="请输入使用人" />
        </Form.Item>
        <Form.Item label="职位" name="position" rules={[{ required: true, message: '请输入职位' }]}>
          <Input placeholder="请输入职位" />
        </Form.Item>
        <Form.Item
          label="电话"
          name="tel"
          rules={[
            { required: true, message: '请输入联系方式' },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号码格式',
            },
          ]}
        >
          <Input placeholder="请输入手机号码" />
        </Form.Item>
        <Form.Item label="头像链接" name="userAvatar" rules={[{ required: false }]}>
          <Input placeholder="请输入头像图片链接（可选）" />
        </Form.Item>
        <Form.Item
          label="密码"
          name="userPassword"
          rules={[
            { required: true, message: '请输入密码 6-20位' },
            { min: 6, message: '密码长度不能少于6个字符' },
            { max: 20, message: '密码长度不能超过20个字符' },
          ]}
        >
          <Input.Password placeholder="设置初始密码" onChange={(e) => setPassword(e.target.value)} />
        </Form.Item>
        <Row align="middle" style={{ marginTop: -10, marginBottom: 16 }}>
          <Col flex="auto">
            <div style={{ display: 'flex', gap: '4px', width: '100%' }}>
              <div
                style={{
                  flex: 1,
                  height: '8px',
                  backgroundColor: strength >= 1 ? '#ff4d4f' : '#f5f5f5',
                  borderRadius: '4px',
                }}
              />
              <div
                style={{
                  flex: 1,
                  height: '8px',
                  backgroundColor: strength >= 2 ? '#faad14' : '#f5f5f5',
                  borderRadius: '4px',
                }}
              />
              <div
                style={{
                  flex: 1,
                  height: '8px',
                  backgroundColor: strength >= 3 ? '#52c41a' : '#f5f5f5',
                  borderRadius: '4px',
                }}
              />
            </div>
          </Col>
          <Col
            style={{
              textAlign: 'right',
              marginLeft: 6,
              color: strength === 1 ? '#ff4d4f' : strength === 2 ? '#faad14' : strength === 3 ? '#52c41a' : '#999',
            }}
          >
            {password ? strengthMap[strength - 1] || '弱' : ''}
          </Col>
        </Row>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Switch checked={enabled} onChange={setEnabled} />
          <span style={{ marginLeft: 8 }}> {enabled ? '启用' : '禁用'} </span>
        </div>
        <Form.Item>
          <Checkbox checked={isAdmin} onChange={(e) => setIsAdmin(e.target.checked)}>
            同时设为管理员
          </Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddUserModal;
