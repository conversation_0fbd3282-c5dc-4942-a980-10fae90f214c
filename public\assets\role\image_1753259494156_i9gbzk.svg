<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5691_22792)">
<g filter="url(#filter0_f_5691_22792)">
<rect x="18.459" y="38.7285" width="18" height="3.27273" fill="#55FF85" fill-opacity="0.8"/>
</g>
<ellipse cx="27.0492" cy="41.1834" rx="2.86364" ry="0.818182" fill="url(#paint0_radial_5691_22792)" fill-opacity="0.7"/>
<circle cx="20.0948" cy="19.0929" r="13.0909" fill="url(#paint1_linear_5691_22792)"/>
<foreignObject x="7.91211" y="7.72852" width="37.4551" height="37.4551"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_5691_22792_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_i_5691_22792)" data-figma-bg-blur-radius="4">
<circle cx="26.6394" cy="26.4558" r="14.7273" fill="#AFFFCA" fill-opacity="0.4"/>
<circle cx="26.6394" cy="26.4558" r="14.4773" stroke="url(#paint2_linear_5691_22792)" stroke-opacity="0.7" stroke-width="0.5"/>
</g>
<g filter="url(#filter2_di_5691_22792)">
<path d="M33.1853 32.5153H21.932C21.5152 32.5153 21.2373 32.7932 21.2373 33.21C21.2373 33.6268 21.5152 33.9046 21.932 33.9046H33.1853C33.602 33.9046 33.8799 33.6268 33.8799 33.21C33.8799 32.7932 33.5324 32.5153 33.1853 32.5153ZM19.9173 25.291C19.5005 25.291 19.2227 25.5689 19.2227 25.9857V28.7643C19.2227 29.181 19.5005 29.4589 19.9173 29.4589C20.3341 29.4589 20.612 29.181 20.612 28.7643V25.9857C20.612 25.5689 20.3341 25.291 19.9173 25.291ZM35.1995 25.291C34.7828 25.291 34.5049 25.5689 34.5049 25.9857V28.7643C34.5049 29.181 34.7828 29.4589 35.1995 29.4589C35.6163 29.4589 35.8942 29.181 35.8942 28.7643V25.9857C35.8942 25.5689 35.6163 25.291 35.1995 25.291Z" fill="url(#paint3_linear_5691_22792)"/>
</g>
<g filter="url(#filter3_di_5691_22792)">
<path d="M27.5557 18.2734C28.528 18.2735 29.2918 19.0375 29.292 20.0098C29.2909 20.3481 29.1926 20.6795 29.0078 20.9629C28.9082 21.1156 28.7855 21.2513 28.6455 21.3652C28.4262 21.5438 28.25 21.7904 28.25 22.0732C28.2501 22.4684 28.5707 22.789 28.9658 22.7891H29.6397C32.1402 22.7891 34.1551 24.8032 34.1553 27.3037C34.1553 29.8044 32.1403 31.8193 29.6397 31.8193H25.4717C22.971 31.8193 20.957 29.8044 20.957 27.3037C20.9559 26.7107 21.0715 26.1233 21.2979 25.5752C21.5243 25.0268 21.8569 24.5279 22.2764 24.1084C22.6958 23.689 23.194 23.3564 23.7422 23.1299C24.2905 22.9034 24.8784 22.7879 25.4717 22.7891H26.1455C26.5406 22.7889 26.8612 22.4683 26.8613 22.0732C26.8613 21.7905 26.6851 21.5438 26.4658 21.3652C26.3259 21.2512 26.2031 21.1157 26.1035 20.9629C25.9188 20.6794 25.8204 20.3481 25.8193 20.0098C25.8196 19.1071 26.5833 18.2734 27.5557 18.2734ZM25.126 25.915C24.7581 25.9162 24.4057 26.0632 24.1455 26.3232C23.8852 26.5836 23.7384 26.9366 23.7373 27.3047C23.7384 27.6728 23.8852 28.0258 24.1455 28.2861C24.4057 28.5462 24.7581 28.6931 25.126 28.6943C25.8899 28.6943 26.5156 28.1383 26.5156 27.3047C26.5146 26.9366 26.3677 26.5836 26.1074 26.3232C25.8471 26.0631 25.494 25.9161 25.126 25.915ZM29.9893 25.915C29.6212 25.9161 29.2681 26.063 29.0078 26.3232C28.7475 26.5836 28.6007 26.9366 28.5996 27.3047C28.6007 27.6728 28.7475 28.0258 29.0078 28.2861C29.2681 28.5464 29.6212 28.6933 29.9893 28.6943C30.7531 28.6942 31.3779 28.1382 31.3779 27.3047C31.3769 26.9366 31.23 26.5836 30.9697 26.3232C30.7095 26.0632 30.3571 25.9162 29.9893 25.915Z" fill="url(#paint4_linear_5691_22792)"/>
</g>
<foreignObject x="25.1855" y="24.002" width="16" height="16"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_2_5691_22792_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter4_i_5691_22792)" data-figma-bg-blur-radius="4">
<circle cx="33.1855" cy="32.002" r="4" fill="#20E963" fill-opacity="0.4"/>
<circle cx="33.1855" cy="32.002" r="3.75" stroke="url(#paint5_linear_5691_22792)" stroke-opacity="0.7" stroke-width="0.5"/>
</g>
<g filter="url(#filter5_d_5691_22792)">
<path d="M35.1855 33.4848V34.002H31.2769V33.4848H32.9551V32.5964H31.7769V32.0881H32.9551V31.3102H32.0551C31.9913 31.434 31.9261 31.5504 31.8595 31.6594C31.7928 31.7684 31.7232 31.8657 31.6508 31.9511C31.6247 31.9305 31.5913 31.9055 31.5508 31.876C31.5131 31.8465 31.4725 31.8171 31.429 31.7876C31.3855 31.7581 31.3421 31.7301 31.2986 31.7036C31.2551 31.6771 31.2174 31.655 31.1855 31.6373C31.2696 31.543 31.3493 31.4369 31.4247 31.3191C31.5029 31.1983 31.5754 31.0701 31.6421 30.9345C31.7087 30.7961 31.7682 30.6532 31.8203 30.5058C31.8725 30.3555 31.9174 30.2053 31.9551 30.055L32.4899 30.1743C32.458 30.2804 32.4247 30.385 32.3899 30.4881C32.358 30.5913 32.3232 30.6944 32.2855 30.7975H32.9551V30.002H33.4986V30.7975H34.9638V31.3102H33.4986V32.0881H34.7986V32.5964H33.4986V33.4848H35.1855Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_5691_22792" x="8.05898" y="28.3285" width="38.8" height="24.0734" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.2" result="effect1_foregroundBlur_5691_22792"/>
</filter>
<filter id="filter1_i_5691_22792" x="7.91211" y="7.72852" width="37.4551" height="37.4551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0581084 0 0 0 0 0.681159 0 0 0 0 0.805769 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_22792"/>
</filter>
<clipPath id="bgblur_1_5691_22792_clip_path" transform="translate(-7.91211 -7.72852)"><circle cx="26.6394" cy="26.4558" r="14.7273"/>
</clipPath><filter id="filter2_di_5691_22792" x="18.2227" y="25.291" width="18.6719" height="10.6133" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.172549 0 0 0 0 0.733333 0 0 0 0 0.133333 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_22792"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_22792" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670656 0 0 0 0 0.986538 0 0 0 0 0.728568 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_22792"/>
</filter>
<filter id="filter3_di_5691_22792" x="19.957" y="18.2734" width="15.1992" height="15.5469" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.171807 0 0 0 0 0.732692 0 0 0 0 0.131744 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_22792"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_22792" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670656 0 0 0 0 0.986538 0 0 0 0 0.728568 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_5691_22792"/>
</filter>
<filter id="filter4_i_5691_22792" x="25.1855" y="24.002" width="16" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0581084 0 0 0 0 0.681159 0 0 0 0 0.805769 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5691_22792"/>
</filter>
<clipPath id="bgblur_2_5691_22792_clip_path" transform="translate(-25.1855 -24.002)"><circle cx="33.1855" cy="32.002" r="4"/>
</clipPath><filter id="filter5_d_5691_22792" x="29.1855" y="29.002" width="8" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0508136 0 0 0 0 0.880769 0 0 0 0 0.592089 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5691_22792"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5691_22792" result="shape"/>
</filter>
<radialGradient id="paint0_radial_5691_22792" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(27.0492 41.1834) scale(2.86364 0.818182)">
<stop offset="0.508354" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_5691_22792" x1="20.0948" y1="6.00195" x2="20.0948" y2="32.1838" gradientUnits="userSpaceOnUse">
<stop stop-color="#37970C"/>
<stop offset="1" stop-color="#54C81B"/>
</linearGradient>
<linearGradient id="paint2_linear_5691_22792" x1="13.2212" y1="15.6558" x2="38.7485" y2="38.8922" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAFFD6"/>
<stop offset="1" stop-color="#6BFF81"/>
</linearGradient>
<linearGradient id="paint3_linear_5691_22792" x1="27.5584" y1="25.291" x2="27.5584" y2="33.9046" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EFFFF4"/>
</linearGradient>
<linearGradient id="paint4_linear_5691_22792" x1="27.5562" y1="18.2734" x2="27.5562" y2="31.8193" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EFFFF4"/>
</linearGradient>
<linearGradient id="paint5_linear_5691_22792" x1="29.5411" y1="29.0686" x2="36.4744" y2="35.3797" gradientUnits="userSpaceOnUse">
<stop stop-color="#BAFFD6"/>
<stop offset="1" stop-color="#6BFF81"/>
</linearGradient>
<clipPath id="clip0_5691_22792">
<rect width="48" height="48" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
