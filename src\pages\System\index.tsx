import React, { useState } from 'react';
import { Button, Input, Select, Table, Switch, Space, Pagination } from 'antd';
import './index.less';
import UserTableSection from './components/UserTableSection';
import AssistantTableSection from './components/AssistantTableSection';
import { useAccess } from '@umijs/max';
import { useEffect } from 'react';
import { history } from '@umijs/max';

const { Option } = Select;

const TABS = [
  { key: 'assistant', label: '助手管理' },
  { key: 'user', label: '用户管理' },
];

const System: React.FC = () => {
  const [currentTab, setCurrentTab] = useState('user');
  const { canAdmin } = useAccess();
  useEffect(() => {
    if (!canAdmin) {
      history.replace('/'); // 非管理员跳转首页
    }
  }, [canAdmin]);

  return (
    <div className="system-page">
      {/* 顶部Tab导航 */}
      <div className="system-header">
        {TABS.map((tab) => (
          <div
            key={tab.key}
            className={`system-header-tab${currentTab === tab.key ? ' active' : ''}`}
            onClick={() => setCurrentTab(tab.key)}
          >
            {tab.label}
          </div>
        ))}
      </div>
      {/* 内容区 */}
      <div className="system-content">
        {currentTab === 'user' && <UserTableSection />}
        {currentTab === 'assistant' && <AssistantTableSection />}
      </div>
    </div>
  );
};

export default System;
