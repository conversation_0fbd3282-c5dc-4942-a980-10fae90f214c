import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Input, Space, message, Modal, Drawer, Popover, Checkbox } from 'antd';
import { SearchOutlined, PlusOutlined, CloseOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import FileUploadModal from './components/FileUploadModal';
import ApiUploadModal from './components/ApiUploadModal';
import DatabaseUploadModal from './components/DatabaseUploadModal';
import PreviewDrawer from './components/PreviewDrawer';
import './index.less';
import {
  addDatasource,
  checkDatasource,
  getDataSource,
  handleApiResponse,
  listUserDataSource,
  deleteDatasource,
} from '@/services/DataLoom/coreDataSourceController';
import SequenceFrame from '@/components/SequenceFrame';
import uploadFileFrame0 from '../../../public/sequence_4/0.png';
import uploadFileFrame1 from '../../../public/sequence_4/1.png';
import uploadFileFrame2 from '../../../public/sequence_4/2.png';
import uploadFileFrame3 from '../../../public/sequence_4/3.png';
import uploadApiFrame0 from '../../../public/sequence_5/0.png';
import uploadApiFrame1 from '../../../public/sequence_5/1.png';
import uploadApiFrame2 from '../../../public/sequence_5/2.png';
import uploadApiFrame3 from '../../../public/sequence_5/3.png';
import uploadDbFrame0 from '../../../public/sequence_6/0.png';
import uploadDbFrame1 from '../../../public/sequence_6/1.png';
import uploadDbFrame2 from '../../../public/sequence_6/2.png';
import uploadDbFrame3 from '../../../public/sequence_6/3.png';

// 数据源类型定义
interface DataSource {
  id: string;
  name: string;
  type: string;
  description: string;
  configuration: string;
  tableNames?: string[];
}

const MySQLDetail: React.FC = () => {
  // 状态管理
  const [dataSource, setDataSource] = useState<DataSource[]>();
  const [loading, setLoading] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
    pageSizeOptions: ['10', '20', '50', '100'],
  });
  const [fileUploadVisible, setFileUploadVisible] = useState(false);
  const [apiUploadVisible, setApiUploadVisible] = useState(false);
  const [databaseUploadVisible, setDatabaseUploadVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [operationType, setOperationType] = useState<'update' | 'upload'>('upload');
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource>();
  const [popoverVisibleId, setPopoverVisibleId] = useState<string | null>(null);
  const [popoverChecked, setPopoverChecked] = useState<string[]>([]);
  const [popoverTableNames, setPopoverTableNames] = useState<string[]>([]);
  const [hoveredUploadFile, setHoveredUploadFile] = useState(false);
  const [hoveredUploadApi, setHoveredUploadApi] = useState(false);
  const [hoveredUploadDb, setHoveredUploadDb] = useState(false);
  const uploadFileFrames = [uploadFileFrame0, uploadFileFrame1, uploadFileFrame2, uploadFileFrame3];
  const uploadApiFrames = [uploadApiFrame0, uploadApiFrame1, uploadApiFrame2, uploadApiFrame3];
  const uploadDbFrames = [uploadDbFrame0, uploadDbFrame1, uploadDbFrame2, uploadDbFrame3];

  // 记录loading开始时间
  const loadingStartTimeRef = React.useRef<number>(0);
  const MIN_LOADING_TIME = 3000; // 最小loading时间，单位毫秒

  // 处理预览显示
  const handlePreviewShow = (record: DataSource) => {
    setSelectedDataSource(record);
    setPreviewVisible(true);
  };

  // 处理清理缓存
  const handleClearCache = (record: DataSource) => {
    Modal.confirm({
      title: null, // 移除标题
      icon: <InfoCircleOutlined style={{ color: '#2868E7' }} />, // 使用信息图标并设置蓝色
      content: `此操作不可撤销确定删除？`, // 更新提示文本
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        type: 'primary',
        style: { backgroundColor: '#2868E7', borderRadius: '4px' },
      },
      cancelButtonProps: {
        style: { borderRadius: '4px' },
      },
      centered: true,
      className: 'custom-info-modal', // 添加自定义类名以便进一步样式定制
      async onOk() {
        try {
          deleteDatasource([Number(record.id)]).then((res) => {
            const { code } = res;
            if (code === 0) {
              message.success('删除成功');
              fetchUserDatasource();
            } else {
              message.error('删除失败');
            }
          });
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const renderPopoverContent = () => (
    <div style={{ width: 260 }}>
      <Checkbox
        indeterminate={popoverChecked.length > 0 && popoverChecked.length < popoverTableNames.length}
        checked={popoverChecked.length === popoverTableNames.length}
        onChange={(e) => setPopoverChecked(e.target.checked ? popoverTableNames : [])}
        style={{ marginBottom: 12 }}
      >
        全选
      </Checkbox>
      <div style={{ maxHeight: 180, overflowY: 'auto', marginBottom: 16 }}>
        <Checkbox.Group
          value={popoverChecked}
          onChange={(list) => setPopoverChecked(list as string[])}
          style={{ width: '100%', display: 'flex', flexDirection: 'column' }}
        >
          {popoverTableNames.map((name) => (
            <div key={name} style={{ marginBottom: 8 }}>
              <Checkbox value={name}>{name}</Checkbox>
            </div>
          ))}
        </Checkbox.Group>
      </div>
      <div style={{ textAlign: 'right' }}>
        <Button
          type="primary"
          onClick={() => {
            setPopoverVisibleId(null);
            if (popoverChecked.length > 0) {
              setPreviewVisible(true);
            }
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );

  // 表格列定义
  const columns: ColumnsType<DataSource> = [
    {
      title: '序号',
      dataIndex: 'id',
      width: 60,
      align: 'center',
    },
    {
      title: '数据源名称',
      dataIndex: 'name',
      width: 300,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      align: 'center',
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '文件名称',
      dataIndex: 'configuration',
      width: 200,
      ellipsis: true,
      render: (text, record) => {
        // 如果是 excel 类型，直接显示 configuration 作为文件名称
        if (record.type === 'excel') {
          return text || '-';
        }
        // 其他类型不显示文件名称
        return '-';
      },
    },
    {
      title: '主机地址',
      dataIndex: 'host',
      width: 150,
      render: (text) => text || '-',
    },
    {
      title: '端口',
      dataIndex: 'port',
      width: 100,
      align: 'center',
      render: (text) => text || '-',
    },
    {
      title: '库名',
      dataIndex: 'database',
      width: 150,
      render: (text) => text || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <Space size={16}>
          {record.type === 'mysql' && (
            <Popover
              content={renderPopoverContent()}
              title={null}
              trigger="click"
              open={popoverVisibleId === record.id}
              onOpenChange={(visible) => {
                if (visible) {
                  setSelectedDataSource(record);
                  setPopoverTableNames(record.tableNames || []);
                  setPopoverChecked([]);
                  setPopoverVisibleId(record.id);
                } else {
                  setPopoverVisibleId(null);
                }
              }}
              placement="right"
            >
              <Button type="link" className="preview-btn">
                预览数据
              </Button>
            </Popover>
          )}
          {record.type === 'api' && (
            <Button type="link" className="preview-btn" onClick={() => handlePreviewShow(record)}>
              预览数据
            </Button>
          )}
          {record.type === 'excel' && (
            <Button type="link" className="preview-btn" onClick={() => handlePreviewShow(record)}>
              预览数据
            </Button>
          )}
          {/* <Button
            type="link"
            className="update-btn"
            onClick={() => {
              setFileUploadVisible(true);
              setOperationType('update');
            }}
          >
            更新数据
          </Button> */}
          <Button type="link" className="clear-btn" onClick={() => handleClearCache(record)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 搜索处理
  const handleSearch = () => {
    // 重置分页到第一页并重新获取数据
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));
    fetchUserDatasource();
  };

  const fetchUserDatasource = () => {
    const params = {
      name: searchText,
      page: pagination.current,
      pageSize: pagination.pageSize,
    };
    listUserDataSource(params)
      .then((res) => {
        const { code, data } = res;
        if (code === 0) {
          const processedData = data?.records.map((item: any) => {
            const defaultValues = {
              host: '',
              port: '',
              dataBaseName: '',
            };

            if (!item.configuration) {
              return { ...item, ...defaultValues };
            }

            try {
              // 检查 configuration 是否为有效的 JSON 字符串
              if (typeof item.configuration === 'string' && (item.configuration.startsWith('{') || item.configuration.startsWith('['))) {
                const config = JSON.parse(item.configuration);
                return {
                  ...item,
                  host: config.host || defaultValues.host,
                  port: config.port || defaultValues.port,
                  database: config.dataBaseName || defaultValues.dataBaseName,
                };
              } else {
                // 如果不是 JSON 字符串，直接使用默认值
                return { ...item, ...defaultValues };
              }
            } catch (error) {
              return { ...item, ...defaultValues };
            }
          });
          setDataSource(processedData as DataSource[]);
          setPagination((prev) => ({ ...prev, total: data.total }));
        }
      })
      .catch((error) => {
        message.error('获取数据源列表失败');
      });
  };

  useEffect(() => {
    fetchUserDatasource();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize]);

  // 表格变化处理
  const handleTableChange = (newPagination: any, filters: any, sorter: any) => {
    setPagination((prev) => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }));
  };

  // 新增：处理标签页移除
  const handleRemoveTable = (tableName: string) => {
    setPopoverChecked((prev) => prev.filter((t) => t !== tableName));
  };

  // 处理选择变化
  const handleSelectionChange = (selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 处理删除选中项
  const handleDeleteSelected = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的数据');
      return;
    }

    Modal.confirm({
      title: null,
      icon: <InfoCircleOutlined style={{ color: '#2868E7' }} />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 条数据吗？`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        type: 'primary',
        style: { backgroundColor: '#2868E7', borderRadius: '4px' },
      },
      cancelButtonProps: {
        style: { borderRadius: '4px' },
      },
      centered: true,
      className: 'custom-info-modal',
      onOk() {
        // TODO: 调用删除接口
        deleteDatasource(selectedRowKeys.map((key) => Number(key)))
          .then((res) => {
            const { code, data } = res;
            if (code === 0) {
              message.success('删除成功');
              setSelectedRowKeys([]);
              fetchUserDatasource();
            }
          })
          .catch((error) => {
            message.error('删除失败');
          });
      },
    });
  };

  return (
    <div className="mysql-detail">
      <div className="mysql-content">
        <div className="mysql-section">
          {/* 上传区域 */}
          <div className="upload-section">
            <Space size={24}>
              <Card
                hoverable
                onClick={() => setFileUploadVisible(true)}
                onMouseEnter={() => setHoveredUploadFile(true)}
                onMouseLeave={() => setHoveredUploadFile(false)}
              >
                <div className="card-content">
                  <div className="icon-section">
                    <SequenceFrame images={uploadFileFrames} play={hoveredUploadFile} />
                  </div>
                  <div className="text-section">
                    <h3>文件上传</h3>
                  </div>
                </div>
              </Card>
              <Card
                hoverable
                onClick={() => setApiUploadVisible(true)}
                onMouseEnter={() => setHoveredUploadApi(true)}
                onMouseLeave={() => setHoveredUploadApi(false)}
              >
                <div className="card-content">
                  <div className="icon-section">
                    <SequenceFrame images={uploadApiFrames} play={hoveredUploadApi} />
                  </div>
                  <div className="text-section">
                    <h3>API上传</h3>
                  </div>
                </div>
              </Card>
              <Card
                hoverable
                onClick={() => setDatabaseUploadVisible(true)}
                onMouseEnter={() => setHoveredUploadDb(true)}
                onMouseLeave={() => setHoveredUploadDb(false)}
              >
                <div className="card-content">
                  <div className="icon-section">
                    <SequenceFrame images={uploadDbFrames} play={hoveredUploadDb} />
                  </div>
                  <div className="text-section">
                    <h3>数据库上传</h3>
                  </div>
                </div>
              </Card>
            </Space>
          </div>

          {/* 数据源列表 */}
          <Card className="data-list-card">
            <div className="card-header">
              <h3 className="card-title">数据源列表</h3>
              <div className="search-bar">
                <Input
                  placeholder="输入关键词搜索"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 240 }}
                  allowClear
                  suffix={<SearchOutlined />}
                  onPressEnter={handleSearch}
                />
                {/* 删除按钮 */}
                <Button color="danger" variant="solid" onClick={handleDeleteSelected} disabled={selectedRowKeys.length === 0}>
                  删除
                </Button>
              </div>
            </div>
            <Table
              columns={columns}
              dataSource={dataSource}
              rowKey="id"
              pagination={pagination}
              loading={loading}
              onChange={handleTableChange}
              rowClassName="custom-row"
              rowSelection={{
                type: 'checkbox',
                selectedRowKeys,
                onChange: handleSelectionChange,
              }}
            />
          </Card>
        </div>
      </div>

      {/* 预览抽屉 */}
      <PreviewDrawer
        visible={previewVisible}
        dataSource={selectedDataSource}
        selectedTables={popoverChecked}
        onClose={() => setPreviewVisible(false)}
        onRemoveTable={handleRemoveTable}
      />

      {/* 上传弹窗 */}
      <FileUploadModal
        visible={fileUploadVisible}
        onCancel={() => {
          setFileUploadVisible(false);
          setOperationType('upload');
        }}
        onOk={() => {
          setFileUploadVisible(false);
          fetchUserDatasource();
        }}
        operationType={operationType}
      />
      <ApiUploadModal
        visible={apiUploadVisible}
        onCancel={() => setApiUploadVisible(false)}
        onOk={() => {
          setApiUploadVisible(false);
          fetchUserDatasource();
        }}
      />
      <DatabaseUploadModal
        visible={databaseUploadVisible}
        onCancel={() => setDatabaseUploadVisible(false)}
        onOk={() => {
          setDatabaseUploadVisible(false);
          fetchUserDatasource();
        }}
      />
    </div>
  );
};

export default MySQLDetail;
