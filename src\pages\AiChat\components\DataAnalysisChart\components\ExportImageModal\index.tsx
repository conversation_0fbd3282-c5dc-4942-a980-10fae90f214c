import React from 'react';
import { Modal, Radio, Button, message, Space } from 'antd';
import { PictureOutlined } from '@ant-design/icons';
import './index.less';
import html2canvas from 'html2canvas';

interface ExportImageModalProps {
  visible: boolean;
  onCancel: () => void;
  chartInstance?: echarts.ECharts;
}

const ExportImageModal: React.FC<ExportImageModalProps> = React.memo(({ visible, onCancel, chartInstance }) => {
  const [format, setFormat] = React.useState<string>('JPEG');

  const handleFormatChange = (e: any) => {
    setFormat(e.target.value);
  };

  const handleExport = async () => {
    if (!chartInstance) {
      message.error('图表实例不存在，无法导出');
      return;
    }

    try {
      const dataUrl = chartInstance.getDataURL({
        type: format.toLowerCase() as 'jpeg' | 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      });

      // Blob 下载方式更稳妥，兼容性好
      const res = await fetch(dataUrl);
      const blob = await res.blob();
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `chart_export.${format.toLowerCase()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(`导出成功，已保存为 ${format} 格式`);
      onCancel();
    } catch (err) {
      console.error(err);
      message.error('导出失败，请重试');
    }
  };

  return (
    <Modal title="导出图片" open={visible} onCancel={onCancel} onOk={handleExport} width={620} className="export-image-modal common-modal">
      <div className="modal-content">
        <div className="format-section">
          <span className="required-mark">*</span>
          <span className="format-label">图片格式</span>
          <Radio.Group onChange={handleFormatChange} value={format} className="format-options">
            <Radio value="JPEG">JPEG</Radio>
            <Radio value="PNG">PNG</Radio>
          </Radio.Group>
        </div>
      </div>
    </Modal>
  );
});

export default ExportImageModal;
