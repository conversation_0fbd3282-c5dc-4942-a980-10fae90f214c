.welcome-container {
  position: relative;
  margin-top: -72px;
  background-color: #fafafa;
  height: calc(100% + 72px);
  overflow-y: auto;
}
.data-analysis-platform {
  position: relative;
  height: 800px;
  overflow: hidden;

  .background-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
  }

  .hero-section {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 160px;
    .hero-content {
      max-width: 800px;
      .main-title {
        height: 140px;
      }

      .sub-title {
        margin-bottom: 16px;
        color: #4074dc;
        font-size: 88px;
        letter-spacing: 8px;
      }

      .description {
        margin-bottom: 8px;
        color: #262628;
        font-size: 24px;

        &:last-of-type {
          margin-bottom: 30px;
        }
      }

      .action-button {
        display: flex;
        align-items: center;
        padding: 12px 24px;
        background: #4074dc;
        border: none;
        border-radius: 24px;
        color: #fff;
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #4074dc;
        }
        .button-icon {
          margin-left: 8px;
          height: 16px;
        }
      }
    }
  }
}

.app-container {
  position: absolute;
  top: 713px;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  min-height: 360px;
  padding: 25px 50px;
  border-radius: 16px;
  background: #fff;
  box-shadow: 0px 0px 20px 0px rgba(179, 204, 220, 0.24);

  // 头部样式
  .header-section {
    text-align: center;
    margin-bottom: 30px;

    .header-title {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
    }

    .header-description {
      font-size: 14px;
      color: #666;
      max-width: 800px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  // 特性部分样式
  .features-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;

    .feature-card {
      flex: 1 1 calc(33.333% - 40px);
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 30px 15px;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
      max-width: 280px;

      &:hover {
        background-color: #f5f7fa;
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .feature-icon-wrapper {
        img {
          width: 100px;
          height: 100px;
          object-fit: contain;
        }
      }

      .feature-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
      }

      .feature-description {
        width: 85%;
        font-size: 14px;
        color: #666;
        line-height: 1.5;
      }
    }

    // 分隔线样式
    .feature-divider {
      width: 1px;
      height: 170px;
      background-color: #d1d5db;
      align-self: center;
    }
  }
}
// 将背景图移到单独的元素中
.app-container-decoration {
  position: absolute;
  top: 1000px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 1200px;
  height: 264px;
  background-image: url('/assets/29bnrn36.svg');
  background-size: 100% 100%;
  background-position: center;
  z-index: 1;
}
// 添加占位元素
.app-container-wrapper {
  width: 100%;
  max-width: 1200px;
  min-height: 360px;
  padding: 27px;
  margin: 0 auto;
}

.ai-visualization-container {
  width: 100%;
  margin-top: 80px;

  .visualization-content {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    .img-section {
      position: relative;
      flex: 0.55;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      padding: 60px 0;
      border-radius: 12px;
      img {
        width: 75%;
      }
    }
    .img-section-text {
      flex: 0.45;
      display: flex;
      flex-direction: column;
      align-items: start;
      justify-content: flex-start;
      padding: 0 100px 360px 100px;
    }
    .text-section {
      position: absolute;
      left: 42%;
      right: 0;
      bottom: -8%;
      display: flex;
      flex-direction: column;
      align-items: start;
      justify-content: center;
      // px改成百分比
      padding: 2% 10% 2% 10%;
      background-color: #faffff;
      color: #333;
      border-radius: 12px;
      z-index: 2;

      .title {
        color: #262628;
        font-size: 48px;
        font-weight: 100;
        letter-spacing: 8px;
      }

      .subtitle {
        color: #262628;
        font-size: 26px;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 24px;
      }

      .description {
        color: #5c5c64;
        font-size: 14px;
        font-weight: 400;
        line-height: 30px;

        p {
          margin-bottom: 15px;
        }
      }
    }
  }
}

.contact-info-container {
  text-align: center;
  padding: 160px 0;
  margin-top: 520px;
  background-color: #1a1a1a;
  color: white;
  .contact-info-inner {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
  }
  .footer-logo-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .logo {
      width: 120px;
      margin-bottom: 10px;
      height: auto;
    }
  }
  .footer-col {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 120px;
    margin-left: 32px;
    .footer-title {
      font-weight: bold;
      margin-bottom: 20px;
      font-size: 16px;
    }
    div {
      margin-bottom: 16px;
      font-size: 15px;
      color: #fff;
      cursor: pointer;
      transition: color 0.2s;
      &:last-child {
        margin-bottom: 0;
      }
      &.footer-title {
        cursor: default;
        color: #fff;
      }
      &:not(.footer-title):hover {
        color: #4fa3ff;
      }
    }
    .footer-product-item {
      cursor: pointer;
      transition: all 0.2s ease;
      &:hover {
        color: #4fa3ff !important;
        transform: translateX(2px);
      }
    }
  }

  .footer-beian {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #333;
    text-align: center;

    a {
      color: #999;
      font-size: 14px;
      text-decoration: none;
      transition: color 0.2s;

      &:hover {
        color: #4fa3ff;
      }
    }
  }
}
