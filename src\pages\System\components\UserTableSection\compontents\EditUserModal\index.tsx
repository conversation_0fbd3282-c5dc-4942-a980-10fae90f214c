import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Switch, Checkbox, Button, Space, Typography } from 'antd';
import { EyeTwoTone, EyeInvisibleOutlined } from '@ant-design/icons';
import { updateUserByAdmin, updateUserStatus } from '@/services/DataLoom/userController';
import { message } from 'antd';

const { Option } = Select;
const { Text } = Typography;

interface EditUserModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  initialValues?: any;
  onResetPassword: (username: string) => void;
}

const departmentOptions = [
  { label: '影像科', value: '影像科' },
  { label: '检验科', value: '检验科' },
  { label: '内科', value: '内科' },
];

const EditUserModal: React.FC<EditUserModalProps> = ({ visible, onOk, onCancel, initialValues, onResetPassword }) => {
  const [form] = Form.useForm();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [enabled, setEnabled] = useState(initialValues?.enabled ?? true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (initialValues) {
      setEnabled(initialValues.enabled ?? true);
      // 设置表单初始值
      form.setFieldsValue({
        userAccount: initialValues.userAccount,
        userName: initialValues.userName,
        department: initialValues.department,
        position: initialValues.position,
        tel: initialValues.tel,
        userAvatar: initialValues.userAvatar,
        isAdmin: initialValues.userRole === 'admin',
        enabled: initialValues.enabled,
      });
    }
  }, [initialValues, form]);

  const handleEnabledChange = (checked: boolean) => {
    setEnabled(checked);
    form.setFieldsValue({ enabled: checked });
  };

  // 新增：编辑用户完整逻辑
  const handleOk = async () => {
    if (!initialValues?.id) {
      message.error('用户ID不存在');
      return;
    }
    try {
      setLoading(true);
      const values = await form.validateFields();
      // 第一步：更新基本用户信息
      const basicUpdateData = {
        id: initialValues.id,
        userAccount: values.userAccount,
        userName: values.userName,
        userAvatar: values.userAvatar || '',
        userRole: values.isAdmin ? 'admin' : 'user',
        position: values.position,
        department: values.department,
        tel: values.tel,
        status: enabled ? 1 : 0,
      };
      const basicResponse = await updateUserByAdmin(basicUpdateData);
      if (basicResponse.code === 0) {
        // 第二步：处理用户状态变更
        const currentStatus = initialValues.status || 1;
        const newStatus = enabled ? 1 : 0;
        if (currentStatus !== newStatus) {
          try {
            await updateUserStatus({
              id: initialValues.id.toString(),
              status: newStatus.toString(),
            });
            message.success('编辑账号成功，状态已更新');
          } catch (statusError) {
            console.error('更新用户状态失败:', statusError);
            message.warning('账号信息更新成功，但状态设置失败');
          }
        } else {
          message.success('编辑账号成功');
        }
      } else {
        message.error(basicResponse.message || '编辑账号失败');
      }
      form.resetFields();
      onCancel();
      onOk();
    } catch (error) {
      console.error('编辑账号失败:', error);
      message.error('编辑账号失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleResetPasswordClick = () => {
    onCancel();
    if (initialValues?.username) {
      onResetPassword(initialValues.username);
    }
  };

  return (
    <Modal
      title="编辑账号信息"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={700}
      destroyOnHidden
      className="edit-user-modal common-modal"
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label="账号名称"
          name="userAccount"
          rules={[
            { required: true, message: '请输入账号名称' },
            { min: 6, message: '账号名称不得少于6位' },
          ]}
        >
          <Input placeholder="请输入账号名称" />
        </Form.Item>
        <Form.Item label="使用科室" name="department" rules={[{ required: true, message: '请选择使用科室' }]}>
          <Select placeholder="请选择科室">
            {departmentOptions.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label="使用人" name="userName" rules={[{ required: true, message: '请输入使用人' }]}>
          <Input placeholder="请输入使用人" />
        </Form.Item>
        <Form.Item label="职位" name="position" rules={[{ required: true, message: '请输入职位' }]}>
          <Input placeholder="请输入职位" />
        </Form.Item>
        <Form.Item
          label="电话"
          name="tel"
          rules={[
            { required: true, message: '请输入联系方式' },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号码格式',
            },
          ]}
        >
          <Input placeholder="请输入手机号码" />
        </Form.Item>
        <Form.Item label="头像链接" name="userAvatar" rules={[{ required: false }]}>
          <Input placeholder="请输入头像图片链接（可选）" />
        </Form.Item>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Switch checked={enabled} onChange={handleEnabledChange} />
          <span style={{ marginLeft: 8 }}>{enabled ? '启用' : '禁用'}</span>
        </div>
        <Form.Item name="isAdmin" valuePropName="checked" style={{ marginBottom: 0 }}>
          <Checkbox>同时设为管理员</Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditUserModal;
