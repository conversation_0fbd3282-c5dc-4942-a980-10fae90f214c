// 变量定义 - 精确匹配设计稿
@primary-color: #2868e7;
@background-color: #f5f8fa;
@card-background: #ffffff;
@text-color: #333333;
@light-text-color: #666666;
@border-color: #e8e8e8;
@banner-bg: #daeaff;
@sidebar-width: 205px;
@right-sidebar-width: 320px;
@card-border-radius: 8px;
@button-border-radius: 4px;

// 主容器
.analytics-dashboard {
  display: flex;
  width: 100%;
  height: 100%;
  padding-top: 12px;
  background-color: #fafafd;
  overflow: hidden;

  // 左侧边栏
  .sidebar {
    width: 259px;
    padding: 32px 16px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    border-radius: 0px 16px 16px 0px;
    background: #fff;
    box-shadow: 2px 0px 8px 0px rgba(7, 32, 77, 0.06);
    transition: all 0.3s ease;

    &.collapsed {
      width: 60px;
      align-items: center;

      .sidebar-header {
        padding: 16px 0;
        text-align: center;

        span {
          display: none;
        }

        .collapse-icon {
          margin: 0;
        }
      }

      .search-container {
        display: flex;
        justify-content: center;
        padding: 10px 0;

        .search-icon-collapsed {
          width: 16px;
          height: 16px;
          cursor: pointer;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .sidebar-analysts {
        display: none;
      }
    }

    .sidebar-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      height: 28px;

      .collapse-icon {
        cursor: pointer;
        width: 16px;
        height: 16px;
        margin-left: 8px;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .search-container {
      padding: 10px 0 24px 0;

      .search-input {
        border-radius: 4px;

        .search-icon {
          color: #bfbfbf;
        }
      }
    }

    .sidebar-analysts {
      height: calc(100vh - 120px);
      overflow-y: auto;
      overflow-x: hidden;

      .ant-list {
        padding: 0;
        width: 100%;
      }

      .ant-list-item {
        padding: 0;
        border-bottom: none;
        width: 100%;
      }
      .loading-more {
        text-align: center;
        color: #999;
        padding: 10px 0;
        font-size: 12px;
      }

      .no-more-data {
        text-align: center;
        color: #999;
        padding: 10px 0;
        font-size: 12px;
      }

      .sidebar-analyst-item {
        width: 100%;
        height: 40px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: relative;
        transition: all 0.3s;
        box-sizing: border-box;

        &:hover {
          background-color: rgba(0, 0, 0, 0.03);
        }

        &.selected {
          background-color: #e5edfc;

          &::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background-color: #2868e7;
          }
        }

        .analyst-title {
          flex: 1;
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .more-button {
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          visibility: hidden;
          color: @light-text-color;

          &:hover {
            background: none;
          }
        }

        &:hover .more-button {
          visibility: visible;
        }
      }
    }
  }

  // 主内容区
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 24px;
    min-width: 0; // 允许flex项目收缩
    overflow: hidden; // 防止内容溢出

    // 顶部横幅
    .banner {
      background: url('/assets/jgphwe0c.svg') no-repeat;
      background-size: cover;
      border-radius: 16px;
      padding: 60px 0 0 126px;
      margin-bottom: 16px;
      display: flex;
      position: relative;
      overflow: hidden;
      min-height: 206px;
      background-position: right center;

      .banner-content {
        flex: 1;
        z-index: 1;

        .banner-title {
          display: flex;
          align-items: flex-end;
          margin: 0 0 8px 0;

          .title-suffix {
            font-size: 26px;
            color: #191961;
            margin-left: 6px;
          }
        }

        .banner-description {
          font-size: 14px;
          color: #5476b9;
        }
      }
    }

    // 标签页
    .tabs-container {
      display: flex;
      border-bottom: 1px solid @border-color;
      margin-bottom: 16px;
      align-items: center;

      .tabs-wrapper {
        display: flex;

        .tab {
          padding: 12px 16px;
          cursor: pointer;
          font-size: 14px;
          position: relative;

          &.active {
            color: #2868e7;
            font-weight: 600;

            &:after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              width: 100%;
              height: 2px;
              background-color: #2868e7;
            }
          }
        }
      }

      .usage-count {
        margin-left: auto;
        padding: 12px 16px;
        font-size: 12px;
        color: @light-text-color;
      }
    }

    // 分析师卡片网格
    .analyst-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      overflow-x: hidden;
      overflow-y: auto;
      padding-right: 8px;

      @media screen and (max-width: 1920px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media screen and (max-width: 1440px) {
        grid-template-columns: 1fr;
      }

      .analyst-card {
        // min-width: 375px;
        // min-height: 195px;
        border-radius: 20px;
        background-color: #fff;
        padding: 24px;
        display: flex;
        flex-direction: column;
        position: relative;
        border: 1px solid #e8e8e8;
        &.selected {
          border: 1px solid #2868e7;
        }
        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: -2px;
          width: 100%;
          height: 100%;
          background-image: url('/assets/h8cxb386.svg');
          background-repeat: no-repeat;
          background-size: cover;
          border-radius: 20px;
          z-index: 1;
        }

        .card-header {
          position: relative;
          z-index: 2;
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
        }

        .card-icon {
          width: 48px;
          height: 48px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .card-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          position: relative;
          z-index: 2;
        }

        .card-title {
          font-weight: 600;
          font-size: 16px;
        }

        .card-description {
          font-size: 12px;
          color: @light-text-color;
          margin-bottom: 16px;
          flex: 1;
          line-height: 1.5;
        }

        .chat-button {
          border-radius: @button-border-radius;
          height: 32px;
          padding: 0 16px;
          font-size: 12px;
          align-self: flex-start;
        }
      }
    }
  }

  // 右侧边栏容器
  .right-sidebar-container {
    display: flex;
    height: 100%;
    box-shadow: -2px 0px 8px 0px rgba(7, 32, 77, 0.06);
    border-radius: 16px 0px 0px 16px;
    overflow: hidden;
  }
}
.analytics-dashboard-bg {
  background-image: url('/assets/rrtmm8u1.svg');
  background-repeat: repeat;
}

// 自定义 Ant Design 组件样式
:global {
  .ant-btn-primary {
    background-color: #2868e7;
    border-color: #2868e7;

    &:hover,
    &:focus {
      background-color: darken(#2868e7, 5%);
      border-color: darken(#2868e7, 5%);
    }
  }

  .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused {
    border-color: #2868e7;
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  }

  .ant-input:focus,
  .ant-input-focused {
    border-color: #2868e7;
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  }
}

// 添加动画
@keyframes pulse {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}
