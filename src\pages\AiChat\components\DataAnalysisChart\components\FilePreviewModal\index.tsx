import React, { useEffect } from 'react';
import { Modal, message } from 'antd';
import Luckysheet from '@/components/Luckysheet';

interface FilePreviewModalProps {
  visible: boolean;
  onCancel: () => void;
  dataSource?: any[];
  title?: string;
}

const FilePreviewModal: React.FC<FilePreviewModalProps> = ({ visible, onCancel, dataSource = [], title = '文件预览' }) => {
  useEffect(() => {
    if (visible && (!dataSource || dataSource.length === 0)) {
      message.warning('暂无数据可预览');
      onCancel();
    }
  }, [visible, dataSource]);

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width="80%"
      style={{ top: 20 }}
      styles={{ body: { height: 'calc(100vh - 200px)', overflow: 'auto' } }}
      footer={null}
    >
      <Luckysheet data={dataSource} />
    </Modal>
  );
};

export default FilePreviewModal;
