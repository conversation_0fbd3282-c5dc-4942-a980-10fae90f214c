import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Space, Menu, Dropdown, Input, message } from 'antd';
import { SettingOutlined, TableOutlined, BarChartOutlined } from '@ant-design/icons';
import './index.less';
import * as echarts from 'echarts';
import { extractTableFromOption } from '@/utils/echartsToTable';

export interface ColumnType {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  render?: (text: any, record: any) => React.ReactNode;
}

export interface FullScreenChartModalProps {
  /** 是否显示模态框 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 标题 */
  title: string;
  /** 图表内容 */
  children: React.ReactNode;
  /** 模态框宽度 */
  width?: number | string;
  /** 模态框高度 */
  height?: number | string;
  /** 是否显示设置按钮 */
  showSettings?: boolean;
  /** 设置回调 */
  onSettings?: () => void;
  /** 是否显示切换按钮 */
  showToggle?: boolean;
  /** 初始显示模式 */
  initialMode?: 'chart' | 'table';
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 重命名回调 */
  onRename?: () => void;
  /** 删除回调 */
  onDelete?: () => void;
  /** ECharts 配置项 */
  chartOption?: any;
}

interface EChartsOption {
  series?: Array<{
    data?: Array<{
      name: string;
      value: number;
      [key: string]: any;
    }>;
  }>;
}

const FullScreenChartModal: React.FC<FullScreenChartModalProps> = ({
  visible,
  onClose,
  title,
  children,
  width = '45%',
  height = '70vh',
  showSettings = true,
  onSettings,
  showToggle = true,
  initialMode = 'chart',
  style,
  className,
  onRename,
  onDelete,
  chartOption,
}) => {
  const [mode, setMode] = useState<'chart' | 'table'>(initialMode);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState(title);
  const [tableData, setTableData] = useState<{ columns: ColumnType[]; dataSource: any[] }>({ columns: [], dataSource: [] });
  const [tableHeight, setTableHeight] = useState(0);
  const chartContentRef = React.useRef<HTMLDivElement>(null);

  // 计算表格高度
  useEffect(() => {
    if (mode === 'table' && chartContentRef.current) {
      const headerHeight = 40; // 表头高度
      const padding = 24; // 上下padding
      const contentHeight = chartContentRef.current.clientHeight;
      setTableHeight(contentHeight - headerHeight - padding);
    }
  }, [mode, visible]);

  const handleToggle = () => {
    const newMode = mode === 'chart' ? 'table' : 'chart';
    setMode(newMode);

    // 如果切换到表格模式，转换图表数据为表格数据
    if (newMode === 'table' && chartOption) {
      const { columns, dataSource } = extractTableFromOption(chartOption);
      setTableData({ columns, dataSource });
    }

    // 如果切换到图表模式，需要重新初始化图表
    if (newMode === 'chart') {
      // 使用 setTimeout 确保 DOM 已经更新
      setTimeout(() => {
        initChart();
      }, 0);
    } else {
      // 如果切换到表格模式，清理图表实例
      cleanup();
    }
  };

  const handleSettings = () => {
    onSettings?.();
  };

  const handleRename = () => {
    if (onRename) {
      onRename();
      message.success('重命名成功');
    }
    setIsRenameModalVisible(false);
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个图表吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if (onDelete) {
          onDelete();
          message.success('删除成功');
        }
      },
    });
  };

  const menuItems = [
    {
      key: 'rename',
      label: '重命名',
      onClick: onRename,
    },
    {
      key: 'delete',
      label: '删除',
      onClick: onDelete,
    },
  ];

  // 新增：全屏 ECharts 渲染
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstanceRef = React.useRef<echarts.ECharts | null>(null);

  const initChart = React.useCallback(() => {
    if (!chartRef.current || !chartOption) return;

    // 如果已经存在实例，先销毁
    if (chartInstanceRef.current) {
      chartInstanceRef.current.dispose();
    }

    // 创建新实例
    const chart = echarts.init(chartRef.current);
    chartInstanceRef.current = chart;
    chart.setOption(chartOption);

    // 添加窗口大小变化监听
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [chartOption]);

  // 清理函数
  const cleanup = React.useCallback(() => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.dispose();
      chartInstanceRef.current = null;
    }
  }, []);

  React.useEffect(() => {
    if (visible && mode === 'chart') {
      initChart();
    }
    return () => {
      cleanup();
    };
  }, [visible, mode, initChart, cleanup]);

  return (
    <>
      <Modal
        destroyOnHidden
        open={visible}
        closable={false}
        title={null}
        width={width}
        style={{ top: 20, ...style }}
        styles={{ body: { height, padding: 0 } }}
        wrapClassName={`fullscreen-chart-modal ${className || ''}`}
        maskClosable={false}
        onCancel={onClose}
        afterOpenChange={(opened) => {
          if (opened) {
            initChart();
          }
        }}
        className="fullscreen-chart-modal common-modal"
      >
        <div className="chart-header">
          <span className="chart-title">{title}</span>
          <div className="chart-actions">
            <Space>
              {showToggle && (
                <Button type="text" onClick={handleToggle}>
                  {mode === 'chart' ? '显示数据' : '显示图表'}
                </Button>
              )}
              {showSettings && (
                <Dropdown menu={{ items: menuItems }} trigger={['click']}>
                  <Button type="text" icon={<SettingOutlined />} />
                </Dropdown>
              )}
            </Space>
          </div>
        </div>
        <div className="chart-content" ref={chartContentRef}>
          {mode === 'chart' ? (
            chartOption ? (
              <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
            ) : (
              children
            )
          ) : (
            <Table
              dataSource={tableData.dataSource}
              columns={tableData.columns}
              pagination={false}
              size="small"
              scroll={{ y: tableHeight }}
            />
          )}
        </div>
      </Modal>

      <Modal title="重命名图表" open={isRenameModalVisible} onOk={handleRename} onCancel={() => setIsRenameModalVisible(false)}>
        <Input value={newTitle} onChange={(e) => setNewTitle(e.target.value)} placeholder="请输入新的图表名称" />
      </Modal>
    </>
  );
};

export default FullScreenChartModal;
