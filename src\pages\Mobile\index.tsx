import React, { useEffect } from 'react';
import { Typography, Button } from 'antd';
import './index.less';

const { Title, Paragraph } = Typography;

const MobilePage: React.FC = () => {
  useEffect(() => {
    document.documentElement.classList.add('mobile-device');
    document.body.classList.add('mobile-device');
    return () => {
      document.documentElement.classList.remove('mobile-device');
      document.body.classList.remove('mobile-device');
    };
  }, []);

  return (
    <div className="mobile-page">
      <div className="content-card">
        <Title level={2} className="title">
          🚫 移动端访问提示
        </Title>
        <Paragraph className="paragraph">
          您正在使用移动设备访问我们的网站。
          <br />
          为了获得最佳体验，我们建议您使用桌面设备访问。
        </Paragraph>
      </div>
    </div>
  );
};

export default MobilePage;
