<svg width="61" height="45" viewBox="0 0 61 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.439758" y="0.5" width="38.0774" height="28.146" rx="4" fill="url(#paint0_linear_1085_2509)"/>
<foreignObject x="10.2046" y="3.79541" width="58.6724" height="48.4951"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4px);clip-path:url(#bgblur_0_1085_2509_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_1085_2509)" data-figma-bg-blur-radius="8">
<rect x="19.2046" y="12.7954" width="40.6724" height="30.4953" rx="6" fill="#ABC7FF" fill-opacity="0.52"/>
<rect x="18.7046" y="12.2954" width="41.6724" height="31.4953" rx="6.5" stroke="url(#paint1_linear_1085_2509)"/>
</g>
<g filter="url(#filter1_d_1085_2509)">
<path d="M48.809 29.1834H30.2661C29.944 29.1834 29.6723 29.3034 29.451 29.5372C29.2363 29.771 29.1289 30.0552 29.1289 30.3775V37.3146C29.1289 37.6367 29.2363 37.9084 29.451 38.1297C29.6658 38.3447 29.9374 38.4518 30.2661 38.4518H48.809C49.1311 38.4518 49.4028 38.3444 49.6241 38.1297C49.8388 37.9147 49.9527 37.6432 49.9527 37.3146V30.3775C49.9527 30.0554 49.8453 29.771 49.6241 29.5372C49.4028 29.3034 49.1311 29.1834 48.809 29.1834ZM33.7345 36.1266C33.1216 36.1266 32.5845 35.899 32.136 35.4505C31.6875 35.002 31.4599 34.4586 31.4599 33.8267C31.4599 33.1948 31.6877 32.6516 32.1362 32.2031C32.5848 31.7546 33.1281 31.527 33.7598 31.527C34.3915 31.527 34.9349 31.7546 35.3834 32.2031C35.8382 32.6579 36.0595 33.195 36.0595 33.8269C36.0595 34.4588 35.832 35.0022 35.3834 35.4507C34.9349 35.899 34.3852 36.1266 33.7345 36.1266ZM48.809 17.6343H30.2661C29.944 17.6343 29.6723 17.7416 29.451 17.9564C29.2363 18.1714 29.1289 18.443 29.1289 18.7717V25.7087C29.1289 26.0309 29.2363 26.3153 29.451 26.5491C29.6658 26.7829 29.9374 26.9029 30.2661 26.9029H48.809C49.1311 26.9029 49.4028 26.7829 49.6241 26.5491C49.8388 26.3153 49.9527 26.0311 49.9527 25.7087V18.7717C49.9527 18.4496 49.8453 18.1779 49.6241 17.9566C49.4028 17.7416 49.1311 17.6343 48.809 17.6343ZM33.7345 24.5776C33.1216 24.5776 32.5845 24.3501 32.136 23.9016C31.6875 23.453 31.4599 22.9097 31.4599 22.2777C31.4599 21.6458 31.6875 21.1025 32.136 20.6539C32.5845 20.2054 33.1279 19.9779 33.7596 19.9779C34.3913 19.9779 34.9346 20.2054 35.3832 20.6539C35.838 21.1087 36.0592 21.6458 36.0592 22.2777C36.0592 22.9097 35.8317 23.453 35.3832 23.9016C34.9349 24.3501 34.3852 24.5776 33.7345 24.5776Z" fill="white"/>
<path d="M30.2656 29.2632H48.8086C49.0711 29.2632 49.2967 29.3475 49.4873 29.5151L49.5664 29.5913C49.773 29.8096 49.873 30.0746 49.873 30.3774V37.3149C49.873 37.5845 49.7925 37.8106 49.6387 37.9966L49.5684 38.0728C49.3627 38.2723 49.111 38.3726 48.8086 38.3726H30.2656C29.9949 38.3725 29.7692 38.2954 29.584 38.1431L29.5068 38.0737C29.3078 37.8682 29.2081 37.6169 29.208 37.3149V30.3774C29.208 30.0743 29.3091 29.8099 29.5098 29.5913L29.5088 29.5903C29.7152 29.3726 29.9664 29.2633 30.2656 29.2632ZM33.7598 31.4478C33.1885 31.4478 32.6858 31.6285 32.2588 31.9839L32.0801 32.147C31.6169 32.6102 31.3809 33.1734 31.3809 33.8267C31.3809 34.4799 31.6169 35.0431 32.0801 35.5063C32.5429 35.9691 33.0997 36.2055 33.7344 36.2056C34.3218 36.2056 34.8319 36.0259 35.2607 35.6704L35.4395 35.5073C35.9027 35.0441 36.1387 34.4799 36.1387 33.8267C36.1386 33.2555 35.9629 32.7577 35.6045 32.3276L35.4395 32.147C34.9762 31.6838 34.4127 31.4478 33.7598 31.4478ZM30.2656 17.7134H48.8086C49.0731 17.7134 49.2992 17.79 49.4893 17.9429L49.5684 18.0132C49.7735 18.2186 49.873 18.4698 49.873 18.772V25.7085C49.873 26.0102 49.7675 26.2753 49.5664 26.4946C49.3598 26.7129 49.1084 26.8237 48.8086 26.8237H30.2656C29.9975 26.8236 29.7726 26.7384 29.5869 26.5718L29.5098 26.4956C29.309 26.277 29.208 26.0115 29.208 25.7085V18.772C29.208 18.463 29.3079 18.2118 29.5059 18.0132C29.7115 17.8136 29.9633 17.7135 30.2656 17.7134ZM33.7598 19.8989C33.1068 19.8989 32.5433 20.1349 32.0801 20.5981C31.6169 21.0614 31.3809 21.6246 31.3809 22.2778C31.3809 22.8493 31.5616 23.3518 31.917 23.7788L32.0801 23.9575C32.5429 24.4203 33.0997 24.6567 33.7344 24.6567C34.322 24.6567 34.832 24.4765 35.2607 24.1206L35.4395 23.9575C35.9026 23.4943 36.1387 22.9309 36.1387 22.2778C36.1387 21.7064 35.9631 21.208 35.6045 20.7778L35.4395 20.5981C34.9763 20.135 34.4127 19.899 33.7598 19.8989Z" stroke="url(#paint2_linear_1085_2509)" stroke-width="0.15873"/>
</g>
<defs>
<filter id="filter0_i_1085_2509" x="10.2046" y="3.79541" width="58.6724" height="48.4951" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.141041 0 0 0 0 0.258674 0 0 0 0 0.493941 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1085_2509"/>
</filter>
<clipPath id="bgblur_0_1085_2509_clip_path" transform="translate(-10.2046 -3.79541)"><rect x="19.2046" y="12.7954" width="40.6724" height="30.4953" rx="6"/>
</clipPath><filter id="filter1_d_1085_2509" x="23.1289" y="11.6343" width="32.8238" height="32.8174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0305541 0 0 0 0 0.255465 0 0 0 0 0.853955 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1085_2509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1085_2509" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1085_2509" x1="5.95355" y1="5.50554" x2="22.0864" y2="31.2992" gradientUnits="userSpaceOnUse">
<stop stop-color="#4074DC"/>
<stop offset="1" stop-color="#699AFD"/>
</linearGradient>
<linearGradient id="paint1_linear_1085_2509" x1="21.3762" y1="14.35" x2="59.7504" y2="37.01" gradientUnits="userSpaceOnUse">
<stop stop-color="#DDE6F7"/>
<stop offset="0.505865" stop-color="#ECF2FF"/>
<stop offset="1" stop-color="#D8E5FF"/>
</linearGradient>
<linearGradient id="paint2_linear_1085_2509" x1="30.2408" y1="18.6955" x2="52.3936" y2="28.5065" gradientUnits="userSpaceOnUse">
<stop stop-color="#C3D1ED"/>
<stop offset="1" stop-color="#BED4FF"/>
</linearGradient>
</defs>
</svg>
