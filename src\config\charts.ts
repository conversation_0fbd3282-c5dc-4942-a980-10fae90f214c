// 图表类型定义
type ChartType = {
  id: string;
  name: string;
  image: string;
  defaultOption: {
    title?: any;
    tooltip?: any;
    legend?: any;
    grid?: any;
    xAxis?: any;
    yAxis?: any;
    series?: any[];
    color?: string[];
    backgroundColor?: string;
    textStyle?: any;
    [key: string]: any;
  };
};

const CHART_STYLE = {
  theme: 'theme1',
  widthRatio: 50,
  showLegend: true,
  showDataLabel: true,
  dataLabelTextSize: '12',
  showDataTips: true,
  showPrefixUnit: false,
  prefixUnitText: '',
  showSuffixUnit: false,
  suffixUnitText: '',
  showAxis: true,
  maxValue: '自动',
  minValue: '自动',
  dataInterval: '自动',
  axisAngle: '0°',
  textSize: '12',
  textColor: '#666',
  showGrid: false,
  gridStyle: '实线',
  gridWidth: '1',
  gridColor: '#e0e0e0',
  showAnimation: true,
};

// 图表类型数据
const CHART_TYPES: ChartType[] = [
  {
    id: 'bar',
    name: '柱状图',
    image: '/assets/mnssb39i.svg',
    defaultOption: {},
  },
  {
    id: 'line',
    name: '折线图',
    image: '/assets/9htgl43h.svg',
    defaultOption: {},
  },
  {
    id: 'pie',
    name: '饼图',
    image: '/assets/acprqf2i.svg',
    defaultOption: {},
  },
  {
    id: 'horizontal-bar',
    name: '条形图',
    image: '/assets/jybw14mr.svg',
    defaultOption: {},
  },
  {
    id: 'scatter',
    name: '散点图',
    image: '/assets/udgwbutc.svg',
    defaultOption: {},
  },
  {
    id: 'combo',
    name: '组合图',
    image: '/assets/7si8jsd7.svg',
    defaultOption: {},
  },
  {
    id: 'funnel',
    name: '漏斗图',
    image: '/assets/jeejfyi6.svg',
    defaultOption: {},
  },
  {
    id: 'word-cloud',
    name: '词云',
    image: '/assets/2r90fnlb.svg',
    defaultOption: {},
  },
];

export { CHART_TYPES, CHART_STYLE };
